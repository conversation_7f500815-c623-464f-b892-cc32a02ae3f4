// 通知服务
import type { NotificationConfig, Visitor } from '../types/business';

export interface NotificationMessage {
  id: string;
  type: 'email' | 'sms' | 'push' | 'system';
  recipient: string;
  subject: string;
  content: string;
  timestamp: string;
  status: 'pending' | 'sent' | 'failed';
  retryCount?: number;
}

export class NotificationService {
  
  /**
   * 发送访客登记通知
   */
  static async sendVisitorRegistrationNotification(visitor: Visitor): Promise<void> {
    const configs = await this.getNotificationConfigs('visitor_registered');
    
    for (const config of configs) {
      if (config.isActive && this.shouldSendNotification(config, visitor)) {
        await this.sendNotificationByConfig(config, visitor, {
          eventType: 'visitor_registered',
          visitorName: visitor.name,
          company: visitor.company,
          purpose: visitor.purpose,
          appointmentTime: visitor.appointmentTime,
        });
      }
    }
  }

  /**
   * 发送审批请求通知
   */
  static async sendApprovalRequestNotification(
    visitor: Visitor, 
    approverRole: string,
    approverEmail?: string
  ): Promise<void> {
    const configs = await this.getNotificationConfigs('approval_required');
    
    for (const config of configs) {
      if (config.isActive && this.shouldSendNotification(config, visitor)) {
        await this.sendNotificationByConfig(config, visitor, {
          eventType: 'approval_required',
          visitorName: visitor.name,
          company: visitor.company,
          purpose: visitor.purpose,
          appointmentTime: visitor.appointmentTime,
          approverRole,
          approverEmail,
        });
      }
    }
  }

  /**
   * 发送访客到达通知
   */
  static async sendVisitorArrivedNotification(visitor: Visitor): Promise<void> {
    const configs = await this.getNotificationConfigs('visitor_arrived');
    
    for (const config of configs) {
      if (config.isActive && this.shouldSendNotification(config, visitor)) {
        await this.sendNotificationByConfig(config, visitor, {
          eventType: 'visitor_arrived',
          visitorName: visitor.name,
          company: visitor.company,
          checkInTime: new Date().toISOString(),
        });
      }
    }
  }

  /**
   * 发送访客离开通知
   */
  static async sendVisitorDepartedNotification(
    visitor: Visitor, 
    actualDuration: number
  ): Promise<void> {
    const configs = await this.getNotificationConfigs('visitor_departed');
    
    for (const config of configs) {
      if (config.isActive && this.shouldSendNotification(config, visitor)) {
        await this.sendNotificationByConfig(config, visitor, {
          eventType: 'visitor_departed',
          visitorName: visitor.name,
          company: visitor.company,
          checkOutTime: new Date().toISOString(),
          actualDuration,
        });
      }
    }
  }

  /**
   * 发送安全警报通知
   */
  static async sendSecurityAlertNotification(
    alertType: string,
    description: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    visitorId?: string,
    location?: string
  ): Promise<void> {
    const configs = await this.getNotificationConfigs('security_alert');
    
    for (const config of configs) {
      if (config.isActive) {
        await this.sendNotificationByConfig(config, null, {
          eventType: 'security_alert',
          alertType,
          description,
          severity,
          visitorId,
          location,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  /**
   * 根据配置发送通知
   */
  private static async sendNotificationByConfig(
    config: NotificationConfig,
    visitor: Visitor | null,
    templateData: Record<string, any>
  ): Promise<void> {
    const recipients = await this.resolveRecipients(config.recipients);
    
    for (const channel of config.channels) {
      if (!channel.enabled) continue;
      
      const content = this.renderTemplate(channel.template || this.getDefaultTemplate(config.eventType), templateData);
      
      for (const recipient of recipients) {
        const message: NotificationMessage = {
          id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: channel.type,
          recipient,
          subject: this.getSubject(config.eventType, templateData),
          content,
          timestamp: new Date().toISOString(),
          status: 'pending',
        };

        await this.sendMessage(message);
      }
    }
  }

  /**
   * 发送消息
   */
  private static async sendMessage(message: NotificationMessage): Promise<void> {
    try {
      switch (message.type) {
        case 'email':
          await this.sendEmail(message);
          break;
        case 'sms':
          await this.sendSMS(message);
          break;
        case 'push':
          await this.sendPushNotification(message);
          break;
        case 'system':
          await this.sendSystemNotification(message);
          break;
      }
      
      message.status = 'sent';
    } catch (error) {
      console.error('发送通知失败:', error);
      message.status = 'failed';
      message.retryCount = (message.retryCount || 0) + 1;
      
      // 如果重试次数少于3次，可以考虑重新发送
      if (message.retryCount < 3) {
        setTimeout(() => this.sendMessage(message), 5000 * message.retryCount);
      }
    }
  }

  /**
   * 发送邮件
   */
  private static async sendEmail(message: NotificationMessage): Promise<void> {
    // 这里应该集成实际的邮件服务
    console.log('发送邮件:', {
      to: message.recipient,
      subject: message.subject,
      content: message.content,
    });
  }

  /**
   * 发送短信
   */
  private static async sendSMS(message: NotificationMessage): Promise<void> {
    // 这里应该集成实际的短信服务
    console.log('发送短信:', {
      to: message.recipient,
      content: message.content,
    });
  }

  /**
   * 发送推送通知
   */
  private static async sendPushNotification(message: NotificationMessage): Promise<void> {
    // 这里应该集成推送服务
    console.log('发送推送通知:', {
      to: message.recipient,
      title: message.subject,
      body: message.content,
    });
  }

  /**
   * 发送系统通知
   */
  private static async sendSystemNotification(message: NotificationMessage): Promise<void> {
    // 这里可以存储到数据库或发送到实时通知系统
    console.log('系统通知:', {
      to: message.recipient,
      title: message.subject,
      content: message.content,
    });
  }

  /**
   * 解析接收人
   */
  private static async resolveRecipients(recipients: NotificationConfig['recipients']): Promise<string[]> {
    const resolved: string[] = [];
    
    for (const recipient of recipients) {
      switch (recipient.type) {
        case 'user':
          // 根据用户ID获取联系方式
          const userContact = await this.getUserContact(recipient.value);
          if (userContact) resolved.push(userContact);
          break;
          
        case 'role':
          // 根据角色获取所有用户的联系方式
          const roleContacts = await this.getRoleContacts(recipient.value);
          resolved.push(...roleContacts);
          break;
          
        case 'department':
          // 根据部门获取所有用户的联系方式
          const deptContacts = await this.getDepartmentContacts(recipient.value);
          resolved.push(...deptContacts);
          break;
      }
    }
    
    return [...new Set(resolved)]; // 去重
  }

  /**
   * 获取用户联系方式
   */
  private static async getUserContact(userId: string): Promise<string | null> {
    // 这里应该从用户服务获取联系方式
    return `user_${userId}@example.com`;
  }

  /**
   * 获取角色用户联系方式
   */
  private static async getRoleContacts(role: string): Promise<string[]> {
    // 这里应该从用户服务获取角色用户
    return [`${role}@example.com`];
  }

  /**
   * 获取部门用户联系方式
   */
  private static async getDepartmentContacts(department: string): Promise<string[]> {
    // 这里应该从用户服务获取部门用户
    return [`${department}@example.com`];
  }

  /**
   * 渲染模板
   */
  private static renderTemplate(template: string, data: Record<string, any>): string {
    let result = template;
    
    for (const [key, value] of Object.entries(data)) {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }
    
    return result;
  }

  /**
   * 获取默认模板
   */
  private static getDefaultTemplate(eventType: string): string {
    const templates = {
      visitor_registered: '访客 {{visitorName}} 来自 {{company}} 已登记，访问目的：{{purpose}}，预约时间：{{appointmentTime}}',
      approval_required: '访客 {{visitorName}} 来自 {{company}} 需要您的审批，访问目的：{{purpose}}',
      visitor_arrived: '访客 {{visitorName}} 来自 {{company}} 已到达并签到',
      visitor_departed: '访客 {{visitorName}} 来自 {{company}} 已离开，访问时长：{{actualDuration}}分钟',
      security_alert: '安全警报：{{alertType}} - {{description}}，严重程度：{{severity}}',
    };
    
    return templates[eventType] || '系统通知：{{eventType}}';
  }

  /**
   * 获取主题
   */
  private static getSubject(eventType: string, data: Record<string, any>): string {
    const subjects = {
      visitor_registered: `访客登记通知 - ${data.visitorName}`,
      approval_required: `访客审批请求 - ${data.visitorName}`,
      visitor_arrived: `访客到达通知 - ${data.visitorName}`,
      visitor_departed: `访客离开通知 - ${data.visitorName}`,
      security_alert: `安全警报 - ${data.severity}`,
    };
    
    return subjects[eventType] || '系统通知';
  }

  /**
   * 检查是否应该发送通知
   */
  private static shouldSendNotification(config: NotificationConfig, visitor: Visitor | null): boolean {
    if (!config.conditions || config.conditions.length === 0) {
      return true;
    }
    
    // 这里可以实现更复杂的条件判断逻辑
    return true;
  }

  /**
   * 获取通知配置
   */
  private static async getNotificationConfigs(eventType: string): Promise<NotificationConfig[]> {
    // 这里应该从后端API获取通知配置
    // return await api.get(`/notification-configs?eventType=${eventType}`);
    
    // 临时返回默认配置
    return [
      {
        id: '1',
        name: '默认通知配置',
        eventType: eventType as any,
        channels: [
          {
            type: 'system',
            enabled: true,
          },
        ],
        recipients: [
          {
            type: 'role',
            value: 'security',
          },
        ],
        isActive: true,
      },
    ];
  }
}
