// 访客业务服务
import { v4 as uuidv4 } from 'uuid';
import type { 
  Visitor, 
  VisitorRegistration, 
  ApprovalWorkflow, 
  VisitorPass,
  VisitorActivity,
  VisitorStatistics 
} from '../types/business';
import type { VisitorStatus } from '../store/visitorStore';

export class VisitorService {
  
  /**
   * 创建访客登记
   */
  static async createVisitorRegistration(registration: VisitorRegistration): Promise<Visitor> {
    const visitor: Visitor = {
      id: uuidv4(),
      name: registration.name,
      phone: registration.phone,
      idCard: registration.idCard,
      company: registration.company,
      purpose: registration.purpose,
      visitee: registration.visitee,
      visiteePhone: registration.visiteePhone,
      department: registration.department,
      expectedDuration: registration.expectedDuration,
      status: 'pending',
      
      // 时间信息
      appointmentTime: registration.appointmentTime,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      
      // 照片和证件
      photo: registration.photo,
      idCardPhoto: registration.idCardFront,
      
      // 其他信息
      vehiclePlate: registration.vehiclePlate,
      accompaniedCount: registration.accompaniedCount,
      isVip: false,
      tags: [],
    };

    // 创建审批工作流
    const workflow = await this.createApprovalWorkflow(visitor.id);
    
    // 这里应该调用后端API保存访客信息
    // await api.post('/visitors', visitor);
    
    return visitor;
  }

  /**
   * 创建审批工作流
   */
  static async createApprovalWorkflow(visitorId: string): Promise<ApprovalWorkflow> {
    const workflow: ApprovalWorkflow = {
      id: uuidv4(),
      visitorId,
      currentStep: 0,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      steps: [
        {
          id: uuidv4(),
          stepName: '被访问人确认',
          approverRole: 'visitee',
          status: 'pending',
          canApprove: true,
          canReject: true,
          canDelegate: false,
        },
        {
          id: uuidv4(),
          stepName: '安保审批',
          approverRole: 'security',
          status: 'pending',
          canApprove: true,
          canReject: true,
          canDelegate: true,
        },
      ],
    };

    // 这里应该调用后端API保存工作流
    // await api.post('/approval-workflows', workflow);
    
    return workflow;
  }

  /**
   * 审批访客
   */
  static async approveVisitor(
    visitorId: string, 
    approverId: string, 
    approverName: string,
    comments?: string
  ): Promise<void> {
    // 获取当前工作流
    const workflow = await this.getApprovalWorkflow(visitorId);
    
    if (!workflow) {
      throw new Error('未找到审批工作流');
    }

    const currentStep = workflow.steps[workflow.currentStep];
    
    if (!currentStep || currentStep.status !== 'pending') {
      throw new Error('当前步骤不可审批');
    }

    // 更新当前步骤
    currentStep.status = 'approved';
    currentStep.approver = approverId;
    currentStep.approverName = approverName;
    currentStep.approvalTime = new Date().toISOString();
    currentStep.comments = comments;

    // 检查是否还有下一步
    if (workflow.currentStep < workflow.steps.length - 1) {
      workflow.currentStep++;
    } else {
      // 所有步骤完成，更新工作流状态
      workflow.status = 'approved';
      workflow.completedAt = new Date().toISOString();
      
      // 生成访客通行证
      await this.generateVisitorPass(visitorId);
    }

    workflow.updatedAt = new Date().toISOString();

    // 这里应该调用后端API更新工作流
    // await api.put(`/approval-workflows/${workflow.id}`, workflow);
  }

  /**
   * 拒绝访客
   */
  static async rejectVisitor(
    visitorId: string, 
    approverId: string, 
    approverName: string,
    reason: string
  ): Promise<void> {
    const workflow = await this.getApprovalWorkflow(visitorId);
    
    if (!workflow) {
      throw new Error('未找到审批工作流');
    }

    const currentStep = workflow.steps[workflow.currentStep];
    
    if (!currentStep || currentStep.status !== 'pending') {
      throw new Error('当前步骤不可审批');
    }

    // 更新当前步骤
    currentStep.status = 'rejected';
    currentStep.approver = approverId;
    currentStep.approverName = approverName;
    currentStep.approvalTime = new Date().toISOString();
    currentStep.comments = reason;

    // 更新工作流状态
    workflow.status = 'rejected';
    workflow.completedAt = new Date().toISOString();
    workflow.updatedAt = new Date().toISOString();

    // 这里应该调用后端API更新工作流
    // await api.put(`/approval-workflows/${workflow.id}`, workflow);
  }

  /**
   * 生成访客通行证
   */
  static async generateVisitorPass(visitorId: string): Promise<VisitorPass> {
    const visitor = await this.getVisitorById(visitorId);
    
    if (!visitor) {
      throw new Error('访客不存在');
    }

    const pass: VisitorPass = {
      id: uuidv4(),
      visitorId,
      passNumber: this.generatePassNumber(),
      accessAreas: ['lobby', 'meeting_room'], // 默认访问区域
      accessLevel: 'visitor',
      validFrom: new Date().toISOString(),
      validUntil: new Date(Date.now() + visitor.expectedDuration * 60 * 1000).toISOString(),
      status: 'active',
      qrCode: this.generateQRCode(visitorId),
    };

    // 这里应该调用后端API保存通行证
    // await api.post('/visitor-passes', pass);
    
    return pass;
  }

  /**
   * 访客签到
   */
  static async checkInVisitor(visitorId: string, location?: string): Promise<void> {
    const pass = await this.getVisitorPass(visitorId);
    
    if (!pass) {
      throw new Error('访客通行证不存在');
    }

    if (pass.status !== 'active') {
      throw new Error('通行证状态异常');
    }

    // 更新通行证
    pass.checkInTime = new Date().toISOString();

    // 记录活动
    await this.recordVisitorActivity({
      id: uuidv4(),
      visitorId,
      passId: pass.id,
      activityType: 'check_in',
      description: '访客签到',
      location,
      timestamp: new Date().toISOString(),
    });

    // 这里应该调用后端API更新通行证
    // await api.put(`/visitor-passes/${pass.id}`, pass);
  }

  /**
   * 访客签退
   */
  static async checkOutVisitor(visitorId: string, location?: string): Promise<void> {
    const pass = await this.getVisitorPass(visitorId);
    
    if (!pass) {
      throw new Error('访客通行证不存在');
    }

    if (!pass.checkInTime) {
      throw new Error('访客尚未签到');
    }

    // 更新通行证
    pass.checkOutTime = new Date().toISOString();
    pass.status = 'expired';
    
    // 计算实际访问时长
    const checkInTime = new Date(pass.checkInTime).getTime();
    const checkOutTime = new Date(pass.checkOutTime).getTime();
    pass.actualDuration = Math.round((checkOutTime - checkInTime) / (1000 * 60));

    // 记录活动
    await this.recordVisitorActivity({
      id: uuidv4(),
      visitorId,
      passId: pass.id,
      activityType: 'check_out',
      description: '访客签退',
      location,
      timestamp: new Date().toISOString(),
    });

    // 这里应该调用后端API更新通行证
    // await api.put(`/visitor-passes/${pass.id}`, pass);
  }

  /**
   * 记录访客活动
   */
  static async recordVisitorActivity(activity: VisitorActivity): Promise<void> {
    // 这里应该调用后端API记录活动
    // await api.post('/visitor-activities', activity);
    console.log('记录访客活动:', activity);
  }

  /**
   * 获取访客信息
   */
  static async getVisitorById(visitorId: string): Promise<Visitor | null> {
    // 这里应该调用后端API获取访客信息
    // return await api.get(`/visitors/${visitorId}`);
    return null; // 临时返回
  }

  /**
   * 获取审批工作流
   */
  static async getApprovalWorkflow(visitorId: string): Promise<ApprovalWorkflow | null> {
    // 这里应该调用后端API获取工作流
    // return await api.get(`/approval-workflows/visitor/${visitorId}`);
    return null; // 临时返回
  }

  /**
   * 获取访客通行证
   */
  static async getVisitorPass(visitorId: string): Promise<VisitorPass | null> {
    // 这里应该调用后端API获取通行证
    // return await api.get(`/visitor-passes/visitor/${visitorId}`);
    return null; // 临时返回
  }

  /**
   * 生成通行证编号
   */
  private static generatePassNumber(): string {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = date.toTimeString().slice(0, 8).replace(/:/g, '');
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `VP${dateStr}${timeStr}${random}`;
  }

  /**
   * 生成二维码
   */
  private static generateQRCode(visitorId: string): string {
    // 这里应该生成实际的二维码
    return `QR_${visitorId}_${Date.now()}`;
  }

  /**
   * 获取访客统计数据
   */
  static async getVisitorStatistics(
    startDate: string, 
    endDate: string
  ): Promise<VisitorStatistics> {
    // 这里应该调用后端API获取统计数据
    // return await api.get(`/visitors/statistics?start=${startDate}&end=${endDate}`);
    
    // 临时返回模拟数据
    return {
      dateRange: { start: startDate, end: endDate },
      totalVisitors: 0,
      newVisitors: 0,
      returningVisitors: 0,
      statusBreakdown: {
        pending: 0,
        approved: 0,
        rejected: 0,
        checked_in: 0,
        checked_out: 0,
        expired: 0,
      },
      hourlyDistribution: [],
      departmentDistribution: [],
      purposeDistribution: [],
      averageDuration: 0,
      securityEvents: 0,
    };
  }
}
