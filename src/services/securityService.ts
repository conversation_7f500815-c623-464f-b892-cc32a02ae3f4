// 安全规则服务
import type { 
  SecurityRule, 
  SecurityCondition, 
  SecurityAction,
  Visitor,
  VisitorPass 
} from '../types/business';

export class SecurityService {
  
  /**
   * 评估访客是否符合安全规则
   */
  static async evaluateVisitorSecurity(visitor: Visitor): Promise<{
    allowed: boolean;
    violations: string[];
    requiredActions: SecurityAction[];
  }> {
    const rules = await this.getActiveSecurityRules();
    const violations: string[] = [];
    const requiredActions: SecurityAction[] = [];
    let allowed = true;

    for (const rule of rules) {
      const ruleResult = this.evaluateRule(rule, visitor);
      
      if (ruleResult.violated) {
        violations.push(`违反规则: ${rule.name} - ${rule.description}`);
        
        // 执行规则动作
        for (const action of rule.actions) {
          if (action.actionType === 'block') {
            allowed = false;
          }
          requiredActions.push(action);
        }
      }
    }

    return {
      allowed,
      violations,
      requiredActions,
    };
  }

  /**
   * 评估单个安全规则
   */
  private static evaluateRule(rule: SecurityRule, visitor: Visitor): {
    violated: boolean;
    details?: string;
  } {
    // 检查所有条件是否满足（AND逻辑）
    for (const condition of rule.conditions) {
      if (!this.evaluateCondition(condition, visitor)) {
        return { violated: false }; // 条件不满足，规则不适用
      }
    }

    // 所有条件都满足，规则被触发
    return { violated: true };
  }

  /**
   * 评估单个条件
   */
  private static evaluateCondition(condition: SecurityCondition, visitor: Visitor): boolean {
    const fieldValue = this.getFieldValue(condition.field, visitor);
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      
      case 'not_equals':
        return fieldValue !== condition.value;
      
      case 'contains':
        return typeof fieldValue === 'string' && 
               fieldValue.toLowerCase().includes(condition.value.toLowerCase());
      
      case 'not_contains':
        return typeof fieldValue === 'string' && 
               !fieldValue.toLowerCase().includes(condition.value.toLowerCase());
      
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
      
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
      
      case 'in':
        return Array.isArray(condition.value) && 
               condition.value.includes(fieldValue);
      
      case 'not_in':
        return Array.isArray(condition.value) && 
               !condition.value.includes(fieldValue);
      
      default:
        return false;
    }
  }

  /**
   * 获取字段值
   */
  private static getFieldValue(fieldPath: string, visitor: Visitor): any {
    const parts = fieldPath.split('.');
    let value: any = visitor;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    // 特殊字段处理
    if (fieldPath.startsWith('time.')) {
      const now = new Date();
      switch (fieldPath) {
        case 'time.hour':
          return now.getHours();
        case 'time.day':
          return now.getDay(); // 0-6, 0是周日
        case 'time.date':
          return now.toISOString().split('T')[0];
        default:
          return undefined;
      }
    }
    
    return value;
  }

  /**
   * 获取活跃的安全规则
   */
  static async getActiveSecurityRules(): Promise<SecurityRule[]> {
    // 这里应该从后端API获取规则
    // const rules = await api.get('/security-rules?active=true');
    
    // 临时返回一些示例规则
    return [
      {
        id: '1',
        name: '工作时间限制',
        description: '非工作时间需要特殊审批',
        ruleType: 'time_restriction',
        conditions: [
          {
            field: 'time.hour',
            operator: 'less_than',
            value: 8,
          },
        ],
        actions: [
          {
            actionType: 'require_approval',
            parameters: { approverRole: 'security' },
          },
        ],
        isActive: true,
        priority: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: '黑名单检查',
        description: '检查访客是否在黑名单中',
        ruleType: 'blacklist',
        conditions: [
          {
            field: 'company',
            operator: 'in',
            value: ['禁止公司A', '禁止公司B'],
          },
        ],
        actions: [
          {
            actionType: 'block',
          },
          {
            actionType: 'send_alert',
            parameters: { 
              message: '黑名单访客尝试登记',
              severity: 'high',
            },
          },
        ],
        isActive: true,
        priority: 10,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
  }

  /**
   * 检查访客通行权限
   */
  static async checkAccessPermission(
    visitorId: string, 
    area: string
  ): Promise<{
    allowed: boolean;
    reason?: string;
    requiresEscort?: boolean;
  }> {
    const pass = await this.getVisitorPass(visitorId);
    
    if (!pass) {
      return {
        allowed: false,
        reason: '访客通行证不存在',
      };
    }

    if (pass.status !== 'active') {
      return {
        allowed: false,
        reason: '通行证状态异常',
      };
    }

    // 检查有效期
    const now = new Date();
    const validUntil = new Date(pass.validUntil);
    
    if (now > validUntil) {
      return {
        allowed: false,
        reason: '通行证已过期',
      };
    }

    // 检查访问区域权限
    if (!pass.accessAreas.includes(area)) {
      return {
        allowed: false,
        reason: '无权限访问该区域',
      };
    }

    // 检查访问级别
    if (pass.accessLevel === 'escort_required') {
      return {
        allowed: true,
        requiresEscort: true,
      };
    }

    return { allowed: true };
  }

  /**
   * 创建安全警报
   */
  static async createSecurityAlert(
    type: 'unauthorized_access' | 'suspicious_behavior' | 'emergency' | 'system_error',
    description: string,
    visitorId?: string,
    location?: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<void> {
    const alert = {
      id: Date.now().toString(),
      type,
      description,
      visitorId,
      location,
      severity,
      timestamp: new Date().toISOString(),
      status: 'active',
    };

    // 这里应该调用后端API创建警报
    // await api.post('/security-alerts', alert);
    
    console.log('安全警报:', alert);

    // 根据严重程度发送通知
    if (severity === 'high' || severity === 'critical') {
      await this.sendEmergencyNotification(alert);
    }
  }

  /**
   * 发送紧急通知
   */
  private static async sendEmergencyNotification(alert: any): Promise<void> {
    // 这里应该发送紧急通知给安保人员
    console.log('发送紧急通知:', alert);
  }

  /**
   * 获取访客通行证
   */
  private static async getVisitorPass(visitorId: string): Promise<VisitorPass | null> {
    // 这里应该调用后端API获取通行证
    // return await api.get(`/visitor-passes/visitor/${visitorId}`);
    return null; // 临时返回
  }

  /**
   * 验证访客身份
   */
  static async verifyVisitorIdentity(
    visitorId: string,
    verificationMethod: 'qr_code' | 'face_recognition' | 'id_card' | 'phone',
    verificationData: string
  ): Promise<{
    verified: boolean;
    confidence?: number;
    reason?: string;
  }> {
    // 这里应该调用相应的验证服务
    switch (verificationMethod) {
      case 'qr_code':
        return this.verifyQRCode(visitorId, verificationData);
      
      case 'face_recognition':
        return this.verifyFaceRecognition(visitorId, verificationData);
      
      case 'id_card':
        return this.verifyIdCard(visitorId, verificationData);
      
      case 'phone':
        return this.verifyPhone(visitorId, verificationData);
      
      default:
        return {
          verified: false,
          reason: '不支持的验证方式',
        };
    }
  }

  /**
   * 验证二维码
   */
  private static async verifyQRCode(visitorId: string, qrCode: string): Promise<{
    verified: boolean;
    reason?: string;
  }> {
    // 这里应该验证二维码的有效性
    return { verified: true };
  }

  /**
   * 人脸识别验证
   */
  private static async verifyFaceRecognition(visitorId: string, faceData: string): Promise<{
    verified: boolean;
    confidence?: number;
    reason?: string;
  }> {
    // 这里应该调用人脸识别服务
    return { 
      verified: true, 
      confidence: 0.95,
    };
  }

  /**
   * 身份证验证
   */
  private static async verifyIdCard(visitorId: string, idCardData: string): Promise<{
    verified: boolean;
    reason?: string;
  }> {
    // 这里应该验证身份证信息
    return { verified: true };
  }

  /**
   * 手机号验证
   */
  private static async verifyPhone(visitorId: string, phone: string): Promise<{
    verified: boolean;
    reason?: string;
  }> {
    // 这里应该验证手机号
    return { verified: true };
  }
}
