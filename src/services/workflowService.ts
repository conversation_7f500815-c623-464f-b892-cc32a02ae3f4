// 业务流程编排服务
import { VisitorService } from './visitorService';
import { SecurityService } from './securityService';
import { NotificationService } from './notificationService';
import type { 
  Visitor, 
  VisitorRegistration, 
  ApprovalWorkflow,
  VisitorPass 
} from '../types/business';

export class WorkflowService {
  
  /**
   * 完整的访客登记流程
   */
  static async processVisitorRegistration(registration: VisitorRegistration): Promise<{
    success: boolean;
    visitor?: Visitor;
    workflow?: ApprovalWorkflow;
    errors?: string[];
    warnings?: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      // 1. 验证登记信息
      const validationResult = await this.validateRegistration(registration);
      if (!validationResult.valid) {
        return {
          success: false,
          errors: validationResult.errors,
        };
      }
      
      // 2. 安全规则检查
      const tempVisitor = await this.createTempVisitor(registration);
      const securityResult = await SecurityService.evaluateVisitorSecurity(tempVisitor);
      
      if (!securityResult.allowed) {
        return {
          success: false,
          errors: ['安全检查未通过', ...securityResult.violations],
        };
      }
      
      if (securityResult.requiredActions.length > 0) {
        warnings.push('需要额外安全措施');
      }
      
      // 3. 创建访客记录
      const visitor = await VisitorService.createVisitorRegistration(registration);
      
      // 4. 创建审批工作流
      const workflow = await VisitorService.createApprovalWorkflow(visitor.id);
      
      // 5. 发送通知
      await NotificationService.sendVisitorRegistrationNotification(visitor);
      await NotificationService.sendApprovalRequestNotification(
        visitor, 
        workflow.steps[0].approverRole
      );
      
      return {
        success: true,
        visitor,
        workflow,
        warnings,
      };
      
    } catch (error) {
      console.error('访客登记流程失败:', error);
      return {
        success: false,
        errors: ['系统错误，请稍后重试'],
      };
    }
  }

  /**
   * 访客审批流程
   */
  static async processVisitorApproval(
    visitorId: string,
    approverId: string,
    approverName: string,
    action: 'approve' | 'reject',
    comments?: string
  ): Promise<{
    success: boolean;
    nextStep?: string;
    completed?: boolean;
    errors?: string[];
  }> {
    try {
      const visitor = await VisitorService.getVisitorById(visitorId);
      if (!visitor) {
        return {
          success: false,
          errors: ['访客不存在'],
        };
      }

      if (action === 'approve') {
        await VisitorService.approveVisitor(visitorId, approverId, approverName, comments);
        
        // 检查是否完成所有审批
        const workflow = await VisitorService.getApprovalWorkflow(visitorId);
        if (workflow?.status === 'approved') {
          // 生成通行证
          const pass = await VisitorService.generateVisitorPass(visitorId);
          
          // 发送通知
          await NotificationService.sendVisitorArrivedNotification(visitor);
          
          return {
            success: true,
            completed: true,
          };
        } else if (workflow) {
          // 通知下一个审批人
          const nextStep = workflow.steps[workflow.currentStep];
          await NotificationService.sendApprovalRequestNotification(
            visitor,
            nextStep.approverRole
          );
          
          return {
            success: true,
            nextStep: nextStep.stepName,
          };
        }
      } else {
        await VisitorService.rejectVisitor(visitorId, approverId, approverName, comments || '');
        
        // 发送拒绝通知
        // await NotificationService.sendVisitorRejectedNotification(visitor, comments);
        
        return {
          success: true,
          completed: true,
        };
      }

      return { success: true };
      
    } catch (error) {
      console.error('审批流程失败:', error);
      return {
        success: false,
        errors: ['审批处理失败'],
      };
    }
  }

  /**
   * 访客签到流程
   */
  static async processVisitorCheckIn(
    visitorId: string,
    location?: string,
    verificationMethod?: 'qr_code' | 'face_recognition' | 'id_card',
    verificationData?: string
  ): Promise<{
    success: boolean;
    pass?: VisitorPass;
    errors?: string[];
    warnings?: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    try {
      // 1. 身份验证（如果提供）
      if (verificationMethod && verificationData) {
        const verifyResult = await SecurityService.verifyVisitorIdentity(
          visitorId,
          verificationMethod,
          verificationData
        );
        
        if (!verifyResult.verified) {
          return {
            success: false,
            errors: ['身份验证失败: ' + (verifyResult.reason || '未知错误')],
          };
        }
        
        if (verifyResult.confidence && verifyResult.confidence < 0.8) {
          warnings.push('身份验证置信度较低');
        }
      }
      
      // 2. 检查通行权限
      const accessResult = await SecurityService.checkAccessPermission(visitorId, 'lobby');
      if (!accessResult.allowed) {
        return {
          success: false,
          errors: ['无权限进入: ' + (accessResult.reason || '未知原因')],
        };
      }
      
      if (accessResult.requiresEscort) {
        warnings.push('需要陪同人员');
      }
      
      // 3. 执行签到
      await VisitorService.checkInVisitor(visitorId, location);
      
      // 4. 获取通行证信息
      const pass = await VisitorService.getVisitorPass(visitorId);
      
      // 5. 发送通知
      const visitor = await VisitorService.getVisitorById(visitorId);
      if (visitor) {
        await NotificationService.sendVisitorArrivedNotification(visitor);
      }
      
      return {
        success: true,
        pass: pass || undefined,
        warnings,
      };
      
    } catch (error) {
      console.error('签到流程失败:', error);
      return {
        success: false,
        errors: ['签到处理失败'],
      };
    }
  }

  /**
   * 访客签退流程
   */
  static async processVisitorCheckOut(
    visitorId: string,
    location?: string
  ): Promise<{
    success: boolean;
    duration?: number;
    errors?: string[];
  }> {
    try {
      // 1. 执行签退
      await VisitorService.checkOutVisitor(visitorId, location);
      
      // 2. 获取访问时长
      const pass = await VisitorService.getVisitorPass(visitorId);
      const duration = pass?.actualDuration;
      
      // 3. 发送通知
      const visitor = await VisitorService.getVisitorById(visitorId);
      if (visitor && duration) {
        await NotificationService.sendVisitorDepartedNotification(visitor, duration);
      }
      
      return {
        success: true,
        duration,
      };
      
    } catch (error) {
      console.error('签退流程失败:', error);
      return {
        success: false,
        errors: ['签退处理失败'],
      };
    }
  }

  /**
   * 紧急撤销访客权限
   */
  static async emergencyRevokeAccess(
    visitorId: string,
    reason: string,
    revokedBy: string
  ): Promise<{
    success: boolean;
    errors?: string[];
  }> {
    try {
      // 1. 撤销通行证
      const pass = await VisitorService.getVisitorPass(visitorId);
      if (pass) {
        // 这里应该更新通行证状态为撤销
        // await api.put(`/visitor-passes/${pass.id}`, { status: 'revoked' });
      }
      
      // 2. 创建安全警报
      await SecurityService.createSecurityAlert(
        'unauthorized_access',
        `紧急撤销访客权限: ${reason}`,
        visitorId,
        undefined,
        'high'
      );
      
      // 3. 记录活动
      await VisitorService.recordVisitorActivity({
        id: Date.now().toString(),
        visitorId,
        passId: pass?.id || '',
        activityType: 'security_alert',
        description: `权限被紧急撤销: ${reason}`,
        timestamp: new Date().toISOString(),
        recordedBy: revokedBy,
      });
      
      return { success: true };
      
    } catch (error) {
      console.error('紧急撤销失败:', error);
      return {
        success: false,
        errors: ['撤销处理失败'],
      };
    }
  }

  /**
   * 验证登记信息
   */
  private static async validateRegistration(registration: VisitorRegistration): Promise<{
    valid: boolean;
    errors: string[];
  }> {
    const errors: string[] = [];
    
    // 基本信息验证
    if (!registration.name?.trim()) {
      errors.push('姓名不能为空');
    }
    
    if (!registration.phone?.trim()) {
      errors.push('手机号不能为空');
    } else if (!/^1[3-9]\d{9}$/.test(registration.phone)) {
      errors.push('手机号格式不正确');
    }
    
    if (!registration.idCard?.trim()) {
      errors.push('身份证号不能为空');
    } else if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(registration.idCard)) {
      errors.push('身份证号格式不正确');
    }
    
    if (!registration.company?.trim()) {
      errors.push('公司名称不能为空');
    }
    
    if (!registration.purpose?.trim()) {
      errors.push('访问目的不能为空');
    }
    
    if (!registration.visitee?.trim()) {
      errors.push('被访问人不能为空');
    }
    
    if (!registration.visiteePhone?.trim()) {
      errors.push('被访问人电话不能为空');
    }
    
    if (!registration.department?.trim()) {
      errors.push('访问部门不能为空');
    }
    
    // 时间验证
    if (!registration.appointmentTime) {
      errors.push('预约时间不能为空');
    } else {
      const appointmentDate = new Date(registration.appointmentTime);
      const now = new Date();
      
      if (appointmentDate < now) {
        errors.push('预约时间不能早于当前时间');
      }
    }
    
    if (!registration.expectedDuration || registration.expectedDuration <= 0) {
      errors.push('预计访问时长必须大于0');
    }
    
    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 创建临时访客对象用于安全检查
   */
  private static async createTempVisitor(registration: VisitorRegistration): Promise<Visitor> {
    return {
      id: 'temp',
      name: registration.name,
      phone: registration.phone,
      idCard: registration.idCard,
      company: registration.company,
      purpose: registration.purpose,
      visitee: registration.visitee,
      visiteePhone: registration.visiteePhone,
      department: registration.department,
      expectedDuration: registration.expectedDuration,
      status: 'pending',
      appointmentTime: registration.appointmentTime,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      vehiclePlate: registration.vehiclePlate,
      accompaniedCount: registration.accompaniedCount,
      isVip: false,
      tags: [],
    };
  }
}
