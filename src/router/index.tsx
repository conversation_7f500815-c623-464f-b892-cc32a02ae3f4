// 路由配置
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';

// 布局组件
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';
import VisitorLayout from '../layouts/VisitorLayout';
import SecurityLayout from '../layouts/SecurityLayout';

// 加载组件
import LoadingSpinner from '../components/common/LoadingSpinner';

// 懒加载页面组件
const LoginPage = lazy(() => import('../pages/auth/LoginPage'));
const DashboardPage = lazy(() => import('../pages/dashboard/DashboardPage'));

// 访客端页面
const VisitorWelcomePage = lazy(() => import('../pages/visitor/WelcomePage'));
const VisitorRegistrationPage = lazy(() => import('../pages/visitor/RegistrationPage'));
const VisitorPhotoPage = lazy(() => import('../pages/visitor/PhotoPage'));
const VisitorWaitingPage = lazy(() => import('../pages/visitor/WaitingPage'));
const VisitorSuccessPage = lazy(() => import('../pages/visitor/SuccessPage'));

// 保安端页面
const SecurityDashboardPage = lazy(() => import('../pages/security/DashboardPage'));
const SecurityApprovalPage = lazy(() => import('../pages/security/ApprovalPage'));
const SecurityMonitorPage = lazy(() => import('../pages/security/MonitorPage'));
const SecurityVisitorListPage = lazy(() => import('../pages/security/VisitorListPage'));

// 管理端页面
const AdminDashboardPage = lazy(() => import('../pages/admin/DashboardPage'));
const AdminUserManagePage = lazy(() => import('../pages/admin/UserManagePage'));
const AdminSystemPage = lazy(() => import('../pages/admin/SystemPage'));
const AdminReportsPage = lazy(() => import('../pages/admin/ReportsPage'));

// 设置页面
const SettingsPage = lazy(() => import('../pages/settings/SettingsPage'));
const HardwareTestPage = lazy(() => import('../pages/settings/HardwareTestPage'));

// 错误页面
const NotFoundPage = lazy(() => import('../pages/error/NotFoundPage'));
const ErrorPage = lazy(() => import('../pages/error/ErrorPage'));

// 路由守卫组件
import ProtectedRoute from '../components/auth/ProtectedRoute';
import RoleBasedRoute from '../components/auth/RoleBasedRoute';

// 包装懒加载组件
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>
    {children}
  </Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/visitor" replace />,
  },
  
  // 认证路由
  {
    path: '/auth',
    element: <AuthLayout />,
    children: [
      {
        path: 'login',
        element: (
          <LazyWrapper>
            <LoginPage />
          </LazyWrapper>
        ),
      },
      {
        index: true,
        element: <Navigate to="/auth/login" replace />,
      },
    ],
  },
  
  // 访客端路由
  {
    path: '/visitor',
    element: <VisitorLayout />,
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <VisitorWelcomePage />
          </LazyWrapper>
        ),
      },
      {
        path: 'registration',
        element: (
          <LazyWrapper>
            <VisitorRegistrationPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'photo',
        element: (
          <LazyWrapper>
            <VisitorPhotoPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'waiting',
        element: (
          <LazyWrapper>
            <VisitorWaitingPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'success',
        element: (
          <LazyWrapper>
            <VisitorSuccessPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  
  // 保安端路由
  {
    path: '/security',
    element: (
      <ProtectedRoute>
        <SecurityLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <SecurityDashboardPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'approval',
        element: (
          <RoleBasedRoute allowedRoles={['security', 'admin']}>
            <LazyWrapper>
              <SecurityApprovalPage />
            </LazyWrapper>
          </RoleBasedRoute>
        ),
      },
      {
        path: 'monitor',
        element: (
          <LazyWrapper>
            <SecurityMonitorPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'visitors',
        element: (
          <LazyWrapper>
            <SecurityVisitorListPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  
  // 管理端路由
  {
    path: '/admin',
    element: (
      <RoleBasedRoute allowedRoles={['admin']}>
        <MainLayout />
      </RoleBasedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <AdminDashboardPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'users',
        element: (
          <LazyWrapper>
            <AdminUserManagePage />
          </LazyWrapper>
        ),
      },
      {
        path: 'system',
        element: (
          <LazyWrapper>
            <AdminSystemPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'reports',
        element: (
          <LazyWrapper>
            <AdminReportsPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  
  // 通用路由
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <DashboardPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  
  // 设置路由
  {
    path: '/settings',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <LazyWrapper>
            <SettingsPage />
          </LazyWrapper>
        ),
      },
      {
        path: 'hardware',
        element: (
          <LazyWrapper>
            <HardwareTestPage />
          </LazyWrapper>
        ),
      },
    ],
  },
  
  // 错误页面
  {
    path: '/error',
    element: (
      <LazyWrapper>
        <ErrorPage />
      </LazyWrapper>
    ),
  },
  {
    path: '*',
    element: (
      <LazyWrapper>
        <NotFoundPage />
      </LazyWrapper>
    ),
  },
]);

export default router;
