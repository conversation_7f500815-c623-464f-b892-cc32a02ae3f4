// 硬件功能调用 Hook
import { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';

// 类型定义
export interface DeviceInfo {
  device_name: string;
  os_version: string;
  app_version: string;
  device_id: string;
  battery_level: number;
  is_charging: boolean;
}

export interface LocationInfo {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

export interface SystemInfo {
  total_memory: number;
  available_memory: number;
  cpu_usage: number;
  storage_total: number;
  storage_available: number;
}

export interface NetworkInfo {
  connection_type: string;
  is_connected: boolean;
  signal_strength: number;
  ip_address: string;
}

export const useHardware = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取设备信息
  const getDeviceInfo = async (): Promise<DeviceInfo | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<DeviceInfo>('get_device_info');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取位置信息
  const getLocation = async (): Promise<LocationInfo | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<LocationInfo>('get_location');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取系统信息
  const getSystemInfo = async (): Promise<SystemInfo | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<SystemInfo>('get_system_info');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取网络信息
  const getNetworkInfo = async (): Promise<NetworkInfo | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<NetworkInfo>('get_network_info');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 震动
  const vibrate = async (duration: number = 500): Promise<string | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<string>('vibrate', { duration });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 显示通知
  const showNotification = async (title: string, message: string): Promise<string | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<string>('show_notification', { title, message });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 读取文件
  const readFile = async (filePath: string): Promise<string | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<string>('read_file', { filePath });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 写入文件
  const writeFile = async (filePath: string, content: string): Promise<string | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<string>('write_file', { filePath, content });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    getDeviceInfo,
    getLocation,
    getSystemInfo,
    getNetworkInfo,
    vibrate,
    showNotification,
    readFile,
    writeFile,
  };
};
