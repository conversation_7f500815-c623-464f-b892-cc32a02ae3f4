// 访客系统硬件操作 Hook
import { useState } from 'react';
import { invoke } from '@tauri-apps/api/core';

// 类型定义
export interface CameraInfo {
  id: string;
  name: string;
  resolution: string;
  status: string;
  supports_face_detection: boolean;
}

export interface PhotoResult {
  success: boolean;
  image_data?: string;
  file_path?: string;
  timestamp: number;
  camera_id: string;
  error_message?: string;
}

export interface PrinterInfo {
  id: string;
  name: string;
  status: string;
  paper_level: number;
  supports_color: boolean;
}

export interface PrintJob {
  id: string;
  document_type: string;
  content: string;
  copies: number;
  status: string;
}

export const useVisitorHardware = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取可用摄像头
  const getAvailableCameras = async (): Promise<CameraInfo[] | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<CameraInfo[]>('get_available_cameras');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 拍摄访客照片
  const captureVisitorPhoto = async (
    cameraId: string, 
    visitorName: string
  ): Promise<PhotoResult | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<PhotoResult>('capture_visitor_photo', {
        cameraId,
        visitorName,
      });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取打印机状态
  const getPrinterStatus = async (): Promise<PrinterInfo[] | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<PrinterInfo[]>('get_printer_status');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 打印访客通行证
  const printVisitorPass = async (
    visitorName: string,
    company: string,
    visitPurpose: string,
    validUntil: string,
    qrCode: string
  ): Promise<PrintJob | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<PrintJob>('print_visitor_pass', {
        visitorName,
        company,
        visitPurpose,
        validUntil,
        qrCode,
      });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 扫描身份证
  const scanIdCard = async (): Promise<any | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<any>('scan_id_card');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 设置双屏内容
  const setDualScreenContent = async (
    screen: 'visitor' | 'security',
    contentType: string,
    data: any
  ): Promise<string | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<string>('set_dual_screen_content', {
        screen,
        contentType,
        data,
      });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 硬件自检
  const hardwareSelfCheck = async (): Promise<any | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<any>('hardware_self_check');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 提交访客登记
  const submitVisitorRegistration = async (visitorData: any): Promise<any | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<any>('submit_visitor_registration', {
        visitorData,
      });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取访客审批状态
  const getVisitorApprovalStatus = async (visitorId: string): Promise<any | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<any>('get_visitor_approval_status', {
        visitorId,
      });
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 同步访客数据
  const syncVisitorData = async (): Promise<any | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<any>('sync_visitor_data');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 获取系统配置
  const getSystemConfig = async (): Promise<any | null> => {
    setLoading(true);
    setError(null);
    try {
      const result = await invoke<any>('get_system_config');
      return result;
    } catch (err) {
      setError(err as string);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    // 硬件操作
    getAvailableCameras,
    captureVisitorPhoto,
    getPrinterStatus,
    printVisitorPass,
    scanIdCard,
    setDualScreenContent,
    hardwareSelfCheck,
    // API操作
    submitVisitorRegistration,
    getVisitorApprovalStatus,
    syncVisitorData,
    getSystemConfig,
  };
};
