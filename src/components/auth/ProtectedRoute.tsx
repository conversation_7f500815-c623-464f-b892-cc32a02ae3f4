// 路由保护组件
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../common/LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  redirectTo = '/auth/login' 
}) => {
  const location = useLocation();
  const { isAuthenticated, isSessionValid, isLoading } = useAuthStore();

  // 如果正在加载认证状态，显示加载器
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // 如果未认证或会话已过期，重定向到登录页
  if (!isAuthenticated || !isSessionValid()) {
    return (
      <Navigate 
        to={redirectTo} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
