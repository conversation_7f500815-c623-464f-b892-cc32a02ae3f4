// 基于角色的路由保护组件
import React from 'react';
import { Navigate } from 'react-router-dom';
import { Result, Button } from 'antd';
import { useAuthStore, type UserRole } from '../../store/authStore';
import ProtectedRoute from './ProtectedRoute';

interface RoleBasedRouteProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  fallbackPath?: string;
  showForbidden?: boolean;
}

const RoleBasedRoute: React.FC<RoleBasedRouteProps> = ({
  children,
  allowedRoles,
  fallbackPath = '/dashboard',
  showForbidden = true,
}) => {
  const { user, hasAnyRole } = useAuthStore();

  return (
    <ProtectedRoute>
      {hasAnyRole(allowedRoles) ? (
        <>{children}</>
      ) : showForbidden ? (
        <Result
          status="403"
          title="403"
          subTitle="抱歉，您没有权限访问此页面。"
          extra={
            <Button type="primary" onClick={() => window.history.back()}>
              返回上一页
            </Button>
          }
        />
      ) : (
        <Navigate to={fallbackPath} replace />
      )}
    </ProtectedRoute>
  );
};

export default RoleBasedRoute;
