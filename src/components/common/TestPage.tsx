// 测试页面组件
import React from 'react';
import { Card, Typography, Button, Space } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const TestPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div style={{ 
      padding: '40px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      minHeight: '100vh',
    }}>
      <Card style={{ maxWidth: 600, width: '100%' }}>
        <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
          <Title level={2}>🎉 应用加载成功！</Title>
          
          <Text>
            如果你能看到这个页面，说明React应用已经正常启动了。
          </Text>
          
          <div>
            <Text strong>当前时间：</Text>
            <Text>{new Date().toLocaleString()}</Text>
          </div>
          
          <Space wrap>
            <Button 
              type="primary" 
              onClick={() => navigate('/visitor')}
            >
              访客端
            </Button>
            <Button 
              onClick={() => navigate('/auth/login')}
            >
              登录页面
            </Button>
            <Button 
              onClick={() => navigate('/settings/hardware')}
            >
              硬件测试
            </Button>
          </Space>
          
          <div style={{ marginTop: 20, fontSize: 12, color: '#666' }}>
            <div>User Agent: {navigator.userAgent}</div>
            <div>Location: {window.location.href}</div>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default TestPage;
