// 加载动画组件
import React from 'react';
import { Spin, Space, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large';
  tip?: string;
  spinning?: boolean;
  children?: React.ReactNode;
  style?: React.CSSProperties;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'large',
  tip = '加载中...',
  spinning = true,
  children,
  style,
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

  if (children) {
    return (
      <Spin 
        spinning={spinning} 
        tip={tip} 
        size={size}
        indicator={antIcon}
        style={style}
      >
        {children}
      </Spin>
    );
  }

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
        ...style,
      }}
    >
      <Space direction="vertical" align="center">
        <Spin 
          indicator={antIcon} 
          size={size}
        />
        {tip && (
          <Text type="secondary" style={{ marginTop: 8 }}>
            {tip}
          </Text>
        )}
      </Space>
    </div>
  );
};

export default LoadingSpinner;
