// 摄像头拍照组件 - 使用Web API
import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>ton, Card, Space, Alert, Select, message } from 'antd';
import { CameraOutlined, ReloadOutlined, CheckOutlined } from '@ant-design/icons';

const { Option } = Select;

interface CameraCaptureProps {
  onCapture: (imageData: string) => void;
  onError?: (error: string) => void;
}

interface MediaDeviceInfo {
  deviceId: string;
  label: string;
  kind: string;
}

const CameraCapture: React.FC<CameraCaptureProps> = ({ onCapture, onError }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取摄像头设备列表
  useEffect(() => {
    const getDevices = async () => {
      try {
        // 请求摄像头权限
        await navigator.mediaDevices.getUserMedia({ video: true });
        
        // 获取设备列表
        const deviceList = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = deviceList.filter(device => device.kind === 'videoinput');
        
        setDevices(videoDevices.map(device => ({
          deviceId: device.deviceId,
          label: device.label || `摄像头 ${device.deviceId.slice(0, 8)}`,
          kind: device.kind,
        })));
        
        if (videoDevices.length > 0) {
          setSelectedDevice(videoDevices[0].deviceId);
        }
      } catch (err) {
        const errorMsg = '无法访问摄像头，请检查权限设置';
        setError(errorMsg);
        onError?.(errorMsg);
        console.error('获取摄像头设备失败:', err);
      }
    };

    getDevices();
  }, [onError]);

  // 启动摄像头流
  const startStream = async (deviceId: string) => {
    try {
      // 停止之前的流
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }

      const constraints = {
        video: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'user', // 优先使用前置摄像头
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
        setIsStreaming(true);
        setError(null);
      }
    } catch (err) {
      const errorMsg = '启动摄像头失败，请检查设备连接';
      setError(errorMsg);
      onError?.(errorMsg);
      console.error('启动摄像头失败:', err);
    }
  };

  // 停止摄像头流
  const stopStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setIsStreaming(false);
  };

  // 切换摄像头
  useEffect(() => {
    if (selectedDevice) {
      startStream(selectedDevice);
    }
    
    return () => {
      stopStream();
    };
  }, [selectedDevice]);

  // 拍摄照片
  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) {
      message.error('摄像头未就绪');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) {
      message.error('无法获取画布上下文');
      return;
    }

    // 设置画布尺寸
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // 绘制当前视频帧到画布
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // 转换为base64图片数据
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedImage(imageData);
    onCapture(imageData);
    
    message.success('照片拍摄成功！');
  };

  // 重新拍摄
  const retakePhoto = () => {
    setCapturedImage(null);
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopStream();
    };
  }, []);

  if (error) {
    return (
      <Alert
        message="摄像头访问失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={() => window.location.reload()}>
            重新加载
          </Button>
        }
      />
    );
  }

  return (
    <div>
      {/* 摄像头选择 */}
      {devices.length > 1 && (
        <div style={{ marginBottom: 16 }}>
          <Select
            value={selectedDevice}
            onChange={setSelectedDevice}
            style={{ width: '100%' }}
            placeholder="选择摄像头"
          >
            {devices.map(device => (
              <Option key={device.deviceId} value={device.deviceId}>
                {device.label}
              </Option>
            ))}
          </Select>
        </div>
      )}

      {/* 视频预览 */}
      <div style={{ position: 'relative', marginBottom: 16 }}>
        <video
          ref={videoRef}
          style={{
            width: '100%',
            maxWidth: '400px',
            height: 'auto',
            borderRadius: 8,
            backgroundColor: '#000',
            display: capturedImage ? 'none' : 'block',
          }}
          autoPlay
          muted
          playsInline
        />
        
        {/* 拍摄的照片预览 */}
        {capturedImage && (
          <img
            src={capturedImage}
            alt="拍摄的照片"
            style={{
              width: '100%',
              maxWidth: '400px',
              height: 'auto',
              borderRadius: 8,
            }}
          />
        )}
        
        {/* 隐藏的画布用于拍照 */}
        <canvas
          ref={canvasRef}
          style={{ display: 'none' }}
        />
      </div>

      {/* 控制按钮 */}
      <Space>
        {!capturedImage ? (
          <Button
            type="primary"
            icon={<CameraOutlined />}
            onClick={capturePhoto}
            disabled={!isStreaming}
            size="large"
          >
            拍摄照片
          </Button>
        ) : (
          <>
            <Button
              icon={<ReloadOutlined />}
              onClick={retakePhoto}
              size="large"
            >
              重新拍摄
            </Button>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              size="large"
            >
              确认照片
            </Button>
          </>
        )}
      </Space>

      {/* 状态提示 */}
      {isStreaming && !capturedImage && (
        <div style={{ marginTop: 8, color: '#52c41a', fontSize: 12 }}>
          ● 摄像头已就绪，点击拍摄按钮拍照
        </div>
      )}
    </div>
  );
};

export default CameraCapture;
