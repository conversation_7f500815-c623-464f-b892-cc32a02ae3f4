// 应用全局状态管理
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface AppState {
  // 应用基础状态
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark';
  language: 'zh' | 'en';
  
  // 设备信息
  deviceInfo: {
    deviceId: string;
    deviceName: string;
    osVersion: string;
    appVersion: string;
    batteryLevel: number;
    isCharging: boolean;
  } | null;
  
  // 网络状态
  networkInfo: {
    isConnected: boolean;
    connectionType: string;
    signalStrength: number;
  } | null;
  
  // 应用配置
  config: {
    apiBaseUrl: string;
    enableOfflineMode: boolean;
    autoSync: boolean;
    syncInterval: number; // 分钟
  };
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: 'zh' | 'en') => void;
  setDeviceInfo: (deviceInfo: AppState['deviceInfo']) => void;
  setNetworkInfo: (networkInfo: AppState['networkInfo']) => void;
  updateConfig: (config: Partial<AppState['config']>) => void;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  isLoading: false,
  error: null,
  theme: 'light' as const,
  language: 'zh' as const,
  deviceInfo: null,
  networkInfo: null,
  config: {
    apiBaseUrl: process.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
    enableOfflineMode: true,
    autoSync: true,
    syncInterval: 5,
  },
};

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),
      
      setTheme: (theme) => set({ theme }),
      
      setLanguage: (language) => set({ language }),
      
      setDeviceInfo: (deviceInfo) => set({ deviceInfo }),
      
      setNetworkInfo: (networkInfo) => set({ networkInfo }),
      
      updateConfig: (configUpdate) => 
        set((state) => ({
          config: { ...state.config, ...configUpdate }
        })),
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState),
    }),
    {
      name: 'app-store',
    }
  )
);
