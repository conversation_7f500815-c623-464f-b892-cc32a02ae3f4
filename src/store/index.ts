// 全局状态管理 - 主入口
export { useAppStore } from './appStore';
export { useVisitorStore } from './visitorStore';
export { useAuthStore } from './authStore';
export { useHardwareStore } from './hardwareStore';

// 导出类型
export type { AppState } from './appStore';
export type { VisitorState, Visitor, VisitorStatus } from './visitorStore';
export type { AuthState, User, UserRole } from './authStore';
export type { HardwareState } from './hardwareStore';
