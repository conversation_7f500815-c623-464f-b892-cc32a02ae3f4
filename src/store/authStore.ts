// 认证状态管理
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type UserRole = 'visitor' | 'security' | 'admin' | 'employee';

export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  department: string;
  avatar?: string;
  permissions: string[];
  isActive: boolean;
  lastLoginTime?: string;
  createdAt: string;
}

export interface AuthState {
  // 认证状态
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  
  // 权限
  permissions: string[];
  
  // 登录状态
  isLoading: boolean;
  error: string | null;
  
  // 会话信息
  sessionExpiry: number | null;
  lastActivity: number;
  
  // Actions
  login: (credentials: { username: string; password: string }) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  setUser: (user: User) => void;
  setToken: (token: string, refreshToken?: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 权限检查
  hasPermission: (permission: string) => boolean;
  hasRole: (role: UserRole) => boolean;
  hasAnyRole: (roles: UserRole[]) => boolean;
  
  // 会话管理
  updateActivity: () => void;
  isSessionValid: () => boolean;
  
  // 工具方法
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  isAuthenticated: false,
  user: null,
  token: null,
  refreshToken: null,
  permissions: [],
  isLoading: false,
  error: null,
  sessionExpiry: null,
  lastActivity: Date.now(),
};

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        login: async (credentials) => {
          set({ isLoading: true, error: null });
          
          try {
            // 这里应该调用实际的登录API
            // 现在先模拟登录逻辑
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 模拟用户数据
            const mockUser: User = {
              id: '1',
              username: credentials.username,
              name: '测试用户',
              email: '<EMAIL>',
              phone: '13800138000',
              role: credentials.username === 'admin' ? 'admin' : 'security',
              department: '安保部',
              permissions: credentials.username === 'admin' 
                ? ['visitor:read', 'visitor:write', 'visitor:approve', 'system:admin']
                : ['visitor:read', 'visitor:approve'],
              isActive: true,
              createdAt: new Date().toISOString(),
            };
            
            const mockToken = 'mock-jwt-token-' + Date.now();
            const mockRefreshToken = 'mock-refresh-token-' + Date.now();
            
            set({
              isAuthenticated: true,
              user: mockUser,
              token: mockToken,
              refreshToken: mockRefreshToken,
              permissions: mockUser.permissions,
              sessionExpiry: Date.now() + 8 * 60 * 60 * 1000, // 8小时
              lastActivity: Date.now(),
              isLoading: false,
              error: null,
            });
            
          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : '登录失败',
            });
            throw error;
          }
        },
        
        logout: () => {
          set(initialState);
          // 清除本地存储
          localStorage.removeItem('auth-store');
        },
        
        refreshAuth: async () => {
          const { refreshToken } = get();
          if (!refreshToken) {
            throw new Error('No refresh token available');
          }
          
          set({ isLoading: true });
          
          try {
            // 这里应该调用刷新token的API
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const newToken = 'refreshed-token-' + Date.now();
            
            set({
              token: newToken,
              sessionExpiry: Date.now() + 8 * 60 * 60 * 1000,
              lastActivity: Date.now(),
              isLoading: false,
            });
            
          } catch (error) {
            set({ isLoading: false });
            // 刷新失败，清除认证状态
            get().logout();
            throw error;
          }
        },
        
        setUser: (user) => set({ user, permissions: user.permissions }),
        
        setToken: (token, refreshToken) => 
          set({ 
            token, 
            refreshToken: refreshToken || get().refreshToken,
            sessionExpiry: Date.now() + 8 * 60 * 60 * 1000,
          }),
        
        setLoading: (loading) => set({ isLoading: loading }),
        
        setError: (error) => set({ error }),
        
        hasPermission: (permission) => {
          const { permissions } = get();
          return permissions.includes(permission);
        },
        
        hasRole: (role) => {
          const { user } = get();
          return user?.role === role;
        },
        
        hasAnyRole: (roles) => {
          const { user } = get();
          return user ? roles.includes(user.role) : false;
        },
        
        updateActivity: () => set({ lastActivity: Date.now() }),
        
        isSessionValid: () => {
          const { sessionExpiry, isAuthenticated } = get();
          return isAuthenticated && sessionExpiry ? Date.now() < sessionExpiry : false;
        },
        
        clearError: () => set({ error: null }),
        
        reset: () => set(initialState),
      }),
      {
        name: 'auth-store',
        partialize: (state) => ({
          isAuthenticated: state.isAuthenticated,
          user: state.user,
          token: state.token,
          refreshToken: state.refreshToken,
          permissions: state.permissions,
          sessionExpiry: state.sessionExpiry,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);
