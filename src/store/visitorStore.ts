// 访客状态管理
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type VisitorStatus = 'pending' | 'approved' | 'rejected' | 'checked_in' | 'checked_out' | 'expired';

export interface Visitor {
  id: string;
  name: string;
  phone: string;
  idCard: string;
  company: string;
  purpose: string;
  visitee: string; // 被访问人
  visiteePhone: string;
  department: string;
  expectedDuration: number; // 预计访问时长（分钟）
  status: VisitorStatus;
  
  // 时间信息
  appointmentTime: string; // 预约时间
  checkInTime?: string;
  checkOutTime?: string;
  createdAt: string;
  updatedAt: string;
  
  // 照片和证件
  photo?: string; // base64 或 URL
  idCardPhoto?: string;
  
  // 审批信息
  approver?: string;
  approvalTime?: string;
  approvalNote?: string;
  
  // 其他信息
  vehiclePlate?: string; // 车牌号
  accompaniedCount: number; // 随行人数
  isVip: boolean;
  tags: string[];
}

export interface VisitorState {
  // 访客列表
  visitors: Visitor[];
  currentVisitor: Visitor | null;
  
  // 筛选和搜索
  filters: {
    status: VisitorStatus | 'all';
    dateRange: [string, string] | null;
    searchKeyword: string;
    department: string;
  };
  
  // 分页
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  
  // 加载状态
  loading: boolean;
  error: string | null;
  
  // Actions
  setVisitors: (visitors: Visitor[]) => void;
  addVisitor: (visitor: Visitor) => void;
  updateVisitor: (id: string, updates: Partial<Visitor>) => void;
  removeVisitor: (id: string) => void;
  setCurrentVisitor: (visitor: Visitor | null) => void;
  
  // 筛选和搜索
  setFilters: (filters: Partial<VisitorState['filters']>) => void;
  setPagination: (pagination: Partial<VisitorState['pagination']>) => void;
  
  // 状态管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 业务操作
  approveVisitor: (id: string, approver: string, note?: string) => void;
  rejectVisitor: (id: string, approver: string, note?: string) => void;
  checkInVisitor: (id: string) => void;
  checkOutVisitor: (id: string) => void;
  
  // 工具方法
  getVisitorsByStatus: (status: VisitorStatus) => Visitor[];
  searchVisitors: (keyword: string) => Visitor[];
  clearFilters: () => void;
  reset: () => void;
}

const initialState = {
  visitors: [],
  currentVisitor: null,
  filters: {
    status: 'all' as const,
    dateRange: null,
    searchKeyword: '',
    department: '',
  },
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0,
  },
  loading: false,
  error: null,
};

export const useVisitorStore = create<VisitorState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        setVisitors: (visitors) => set({ visitors }),
        
        addVisitor: (visitor) => 
          set((state) => ({
            visitors: [...state.visitors, visitor],
            pagination: {
              ...state.pagination,
              total: state.pagination.total + 1,
            },
          })),
        
        updateVisitor: (id, updates) =>
          set((state) => ({
            visitors: state.visitors.map((visitor) =>
              visitor.id === id 
                ? { ...visitor, ...updates, updatedAt: new Date().toISOString() }
                : visitor
            ),
          })),
        
        removeVisitor: (id) =>
          set((state) => ({
            visitors: state.visitors.filter((visitor) => visitor.id !== id),
            pagination: {
              ...state.pagination,
              total: Math.max(0, state.pagination.total - 1),
            },
          })),
        
        setCurrentVisitor: (visitor) => set({ currentVisitor: visitor }),
        
        setFilters: (filters) =>
          set((state) => ({
            filters: { ...state.filters, ...filters },
          })),
        
        setPagination: (pagination) =>
          set((state) => ({
            pagination: { ...state.pagination, ...pagination },
          })),
        
        setLoading: (loading) => set({ loading }),
        
        setError: (error) => set({ error }),
        
        approveVisitor: (id, approver, note) =>
          set((state) => ({
            visitors: state.visitors.map((visitor) =>
              visitor.id === id
                ? {
                    ...visitor,
                    status: 'approved' as VisitorStatus,
                    approver,
                    approvalTime: new Date().toISOString(),
                    approvalNote: note,
                    updatedAt: new Date().toISOString(),
                  }
                : visitor
            ),
          })),
        
        rejectVisitor: (id, approver, note) =>
          set((state) => ({
            visitors: state.visitors.map((visitor) =>
              visitor.id === id
                ? {
                    ...visitor,
                    status: 'rejected' as VisitorStatus,
                    approver,
                    approvalTime: new Date().toISOString(),
                    approvalNote: note,
                    updatedAt: new Date().toISOString(),
                  }
                : visitor
            ),
          })),
        
        checkInVisitor: (id) =>
          set((state) => ({
            visitors: state.visitors.map((visitor) =>
              visitor.id === id
                ? {
                    ...visitor,
                    status: 'checked_in' as VisitorStatus,
                    checkInTime: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  }
                : visitor
            ),
          })),
        
        checkOutVisitor: (id) =>
          set((state) => ({
            visitors: state.visitors.map((visitor) =>
              visitor.id === id
                ? {
                    ...visitor,
                    status: 'checked_out' as VisitorStatus,
                    checkOutTime: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                  }
                : visitor
            ),
          })),
        
        getVisitorsByStatus: (status) => {
          const { visitors } = get();
          return visitors.filter((visitor) => visitor.status === status);
        },
        
        searchVisitors: (keyword) => {
          const { visitors } = get();
          const lowerKeyword = keyword.toLowerCase();
          return visitors.filter(
            (visitor) =>
              visitor.name.toLowerCase().includes(lowerKeyword) ||
              visitor.phone.includes(keyword) ||
              visitor.company.toLowerCase().includes(lowerKeyword) ||
              visitor.visitee.toLowerCase().includes(lowerKeyword)
          );
        },
        
        clearFilters: () =>
          set({
            filters: {
              status: 'all',
              dateRange: null,
              searchKeyword: '',
              department: '',
            },
          }),
        
        reset: () => set(initialState),
      }),
      {
        name: 'visitor-store',
        partialize: (state) => ({
          visitors: state.visitors,
          filters: state.filters,
        }),
      }
    ),
    {
      name: 'visitor-store',
    }
  )
);
