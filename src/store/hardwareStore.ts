// 硬件状态管理
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { DeviceInfo, LocationInfo, SystemInfo, NetworkInfo } from '../hooks/useHardware';

export interface HardwareState {
  // 设备信息
  deviceInfo: DeviceInfo | null;
  locationInfo: LocationInfo | null;
  systemInfo: SystemInfo | null;
  networkInfo: NetworkInfo | null;
  
  // 硬件状态
  cameraStatus: 'idle' | 'capturing' | 'error';
  printerStatus: 'idle' | 'printing' | 'error' | 'offline';
  scannerStatus: 'idle' | 'scanning' | 'error';
  
  // 双屏状态
  dualScreenConfig: {
    visitorScreen: {
      width: number;
      height: number;
      position: 'left' | 'right' | 'top' | 'bottom';
    };
    securityScreen: {
      width: number;
      height: number;
      position: 'left' | 'right' | 'top' | 'bottom';
    };
  } | null;
  
  // 加载状态
  loading: boolean;
  error: string | null;
  
  // 最后更新时间
  lastUpdated: number;
  
  // Actions
  setDeviceInfo: (info: DeviceInfo) => void;
  setLocationInfo: (info: LocationInfo) => void;
  setSystemInfo: (info: SystemInfo) => void;
  setNetworkInfo: (info: NetworkInfo) => void;
  
  // 硬件状态管理
  setCameraStatus: (status: HardwareState['cameraStatus']) => void;
  setPrinterStatus: (status: HardwareState['printerStatus']) => void;
  setScannerStatus: (status: HardwareState['scannerStatus']) => void;
  
  // 双屏配置
  setDualScreenConfig: (config: HardwareState['dualScreenConfig']) => void;
  
  // 通用状态
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 工具方法
  updateLastUpdated: () => void;
  isHardwareReady: () => boolean;
  getHardwareStatus: () => {
    camera: boolean;
    printer: boolean;
    scanner: boolean;
    network: boolean;
  };
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  deviceInfo: null,
  locationInfo: null,
  systemInfo: null,
  networkInfo: null,
  cameraStatus: 'idle' as const,
  printerStatus: 'idle' as const,
  scannerStatus: 'idle' as const,
  dualScreenConfig: null,
  loading: false,
  error: null,
  lastUpdated: 0,
};

export const useHardwareStore = create<HardwareState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setDeviceInfo: (info) => 
        set({ 
          deviceInfo: info, 
          lastUpdated: Date.now() 
        }),
      
      setLocationInfo: (info) => 
        set({ 
          locationInfo: info, 
          lastUpdated: Date.now() 
        }),
      
      setSystemInfo: (info) => 
        set({ 
          systemInfo: info, 
          lastUpdated: Date.now() 
        }),
      
      setNetworkInfo: (info) => 
        set({ 
          networkInfo: info, 
          lastUpdated: Date.now() 
        }),
      
      setCameraStatus: (status) => 
        set({ 
          cameraStatus: status, 
          lastUpdated: Date.now() 
        }),
      
      setPrinterStatus: (status) => 
        set({ 
          printerStatus: status, 
          lastUpdated: Date.now() 
        }),
      
      setScannerStatus: (status) => 
        set({ 
          scannerStatus: status, 
          lastUpdated: Date.now() 
        }),
      
      setDualScreenConfig: (config) => 
        set({ 
          dualScreenConfig: config, 
          lastUpdated: Date.now() 
        }),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),
      
      updateLastUpdated: () => set({ lastUpdated: Date.now() }),
      
      isHardwareReady: () => {
        const state = get();
        return (
          state.cameraStatus !== 'error' &&
          state.printerStatus !== 'error' &&
          state.scannerStatus !== 'error' &&
          state.networkInfo?.is_connected === true
        );
      },
      
      getHardwareStatus: () => {
        const state = get();
        return {
          camera: state.cameraStatus !== 'error',
          printer: state.printerStatus !== 'error' && state.printerStatus !== 'offline',
          scanner: state.scannerStatus !== 'error',
          network: state.networkInfo?.is_connected === true,
        };
      },
      
      clearError: () => set({ error: null }),
      
      reset: () => set(initialState),
    }),
    {
      name: 'hardware-store',
    }
  )
);
