// 主应用组件 - 使用新的路由系统
import { useEffect, useState } from 'react';
import { RouterProvider } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, theme, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { router } from './router';
import { useAppStore } from './store/appStore';
import { useHardwareStore } from './store/hardwareStore';
import { useHardware } from './hooks/useHardware';
import ErrorBoundary from './components/common/ErrorBoundary';

// 设置 dayjs 中文
dayjs.locale('zh-cn');

function App() {
  const [appReady, setAppReady] = useState(false);
  const { theme: appTheme } = useAppStore();
  const { setDeviceInfo, setNetworkInfo, setSystemInfo } = useHardwareStore();
  const hardware = useHardware();

  // 初始化应用
  useEffect(() => {
    const initializeApp = async () => {
      try {
        console.log('开始初始化应用...');

        // 设置应用基本状态
        setAppReady(true);

        // 异步初始化硬件信息（不阻塞渲染）
        setTimeout(async () => {
          try {
            console.log('开始初始化硬件信息...');

            // 获取设备信息
            const deviceInfo = await hardware.getDeviceInfo();
            if (deviceInfo) {
              setDeviceInfo(deviceInfo);
              console.log('设备信息初始化完成');
            }

            // 获取网络信息
            const networkInfo = await hardware.getNetworkInfo();
            if (networkInfo) {
              setNetworkInfo(networkInfo);
              console.log('网络信息初始化完成');
            }

            // 获取系统信息
            const systemInfo = await hardware.getSystemInfo();
            if (systemInfo) {
              setSystemInfo(systemInfo);
              console.log('系统信息初始化完成');
            }
          } catch (error) {
            console.error('硬件信息初始化失败:', error);
          }
        }, 100);
      } catch (error) {
        console.error('应用初始化失败:', error);
        setAppReady(true); // 即使失败也要显示应用
      }
    };

    initializeApp();
  }, [hardware, setDeviceInfo, setNetworkInfo, setSystemInfo]);

  // 如果应用还没准备好，显示加载界面
  if (!appReady) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: 16,
        }}
      >
        <Spin size="large" />
        <div style={{ color: '#666' }}>正在初始化应用...</div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          algorithm: appTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 8,
          },
        }}
      >
        <AntdApp>
          <RouterProvider router={router} />
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
