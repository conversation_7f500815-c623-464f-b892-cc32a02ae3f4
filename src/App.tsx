// 主应用组件 - 使用新的路由系统
import { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { router } from './router';
import { useAppStore } from './store/appStore';
import { useHardwareStore } from './store/hardwareStore';
import { useHardware } from './hooks/useHardware';

// 设置 dayjs 中文
dayjs.locale('zh-cn');

function App() {
  const { theme: appTheme } = useAppStore();
  const { setDeviceInfo, setNetworkInfo, setSystemInfo } = useHardwareStore();
  const hardware = useHardware();

  // 初始化硬件信息
  useEffect(() => {
    const initializeHardware = async () => {
      try {
        // 获取设备信息
        const deviceInfo = await hardware.getDeviceInfo();
        if (deviceInfo) {
          setDeviceInfo(deviceInfo);
        }

        // 获取网络信息
        const networkInfo = await hardware.getNetworkInfo();
        if (networkInfo) {
          setNetworkInfo(networkInfo);
        }

        // 获取系统信息
        const systemInfo = await hardware.getSystemInfo();
        if (systemInfo) {
          setSystemInfo(systemInfo);
        }
      } catch (error) {
        console.error('初始化硬件信息失败:', error);
      }
    };

    initializeHardware();
  }, [hardware, setDeviceInfo, setNetworkInfo, setSystemInfo]);

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: appTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 8,
        },
      }}
    >
      <AntdApp>
        <RouterProvider router={router} />
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
