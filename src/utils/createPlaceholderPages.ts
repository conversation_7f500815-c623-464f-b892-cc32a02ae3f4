// 创建占位页面的工具脚本
const placeholderPages = [
  // 访客页面
  'src/pages/visitor/PhotoPage.tsx',
  'src/pages/visitor/WaitingPage.tsx', 
  'src/pages/visitor/SuccessPage.tsx',
  
  // 保安页面
  'src/pages/security/DashboardPage.tsx',
  'src/pages/security/ApprovalPage.tsx',
  'src/pages/security/MonitorPage.tsx',
  'src/pages/security/VisitorListPage.tsx',
  
  // 管理页面
  'src/pages/admin/DashboardPage.tsx',
  'src/pages/admin/UserManagePage.tsx',
  'src/pages/admin/SystemPage.tsx',
  'src/pages/admin/ReportsPage.tsx',
  
  // 设置页面
  'src/pages/settings/SettingsPage.tsx',
  'src/pages/settings/HardwareTestPage.tsx',
];

const createPlaceholderComponent = (pageName: string) => `// ${pageName} - 占位组件
import React from 'react';
import { Card, Typography } from 'antd';

const { Title } = Typography;

const ${pageName.replace(/Page$/, '')}: React.FC = () => {
  return (
    <Card>
      <Title level={3}>${pageName}</Title>
      <p>此页面正在开发中...</p>
    </Card>
  );
};

export default ${pageName.replace(/Page$/, '')};
`;

// 这个文件仅用于记录需要创建的占位页面
export { placeholderPages, createPlaceholderComponent };
