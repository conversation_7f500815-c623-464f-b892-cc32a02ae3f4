// 访客登记页面
import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Steps,
  Card,
  Row,
  Col,
  DatePicker,
  InputNumber,
  Select,
  Upload,
  message,
  Space,
  Typography,
  Divider,
} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  IdcardOutlined,
  BankOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  CameraOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { useVisitorHardware } from '../../hooks/useVisitorHardware';
import { useVisitorStore } from '../../store/visitorStore';
import type { VisitorRegistration } from '../../types/business';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface FormData {
  // 基本信息
  name: string;
  phone: string;
  idCard: string;
  company: string;

  // 访问信息
  purpose: string;
  visitee: string;
  visiteePhone: string;
  department: string;
  appointmentTime: dayjs.Dayjs;
  expectedDuration: number;

  // 附加信息
  vehiclePlate?: string;
  accompaniedCount: number;
  specialRequirements?: string;
}

const RegistrationPage: React.FC = () => {
  const [form] = Form.useForm<FormData>();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);

  const navigate = useNavigate();
  const hardware = useVisitorHardware();
  const { addVisitor } = useVisitorStore();

  const steps = [
    {
      title: '基本信息',
      icon: <UserOutlined />,
      description: '填写个人基本信息',
    },
    {
      title: '访问信息',
      icon: <BankOutlined />,
      description: '填写访问相关信息',
    },
    {
      title: '拍摄照片',
      icon: <CameraOutlined />,
      description: '拍摄个人证件照',
    },
    {
      title: '确认提交',
      icon: <CheckCircleOutlined />,
      description: '确认信息并提交',
    },
  ];

  // 下一步
  const handleNext = async () => {
    try {
      if (currentStep === 0) {
        // 验证基本信息
        await form.validateFields(['name', 'phone', 'idCard', 'company']);
      } else if (currentStep === 1) {
        // 验证访问信息
        await form.validateFields([
          'purpose',
          'visitee',
          'visiteePhone',
          'department',
          'appointmentTime',
          'expectedDuration',
        ]);
      } else if (currentStep === 2) {
        // 检查是否已拍摄照片
        if (!capturedPhoto) {
          message.error('请先拍摄照片');
          return;
        }
      }

      setCurrentStep(currentStep + 1);
    } catch (error) {
      message.error('请完善必填信息');
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  // 拍摄照片
  const handleCapturePhoto = async () => {
    try {
      setLoading(true);
      const formData = form.getFieldsValue();

      // 获取可用摄像头
      const cameras = await hardware.getAvailableCameras();
      if (!cameras || cameras.length === 0) {
        message.error('未找到可用摄像头');
        return;
      }

      // 使用第一个摄像头拍照
      const result = await hardware.captureVisitorPhoto(cameras[0].id, formData.name);

      if (result?.success && result.image_data) {
        setCapturedPhoto(result.image_data);
        message.success('照片拍摄成功');
      } else {
        message.error(result?.error_message || '照片拍摄失败');
      }
    } catch (error) {
      message.error('拍摄照片时发生错误');
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const formData = form.getFieldsValue();

      const registration: VisitorRegistration = {
        name: formData.name,
        phone: formData.phone,
        idCard: formData.idCard,
        company: formData.company,
        purpose: formData.purpose,
        visitee: formData.visitee,
        visiteePhone: formData.visiteePhone,
        department: formData.department,
        appointmentTime: formData.appointmentTime.toISOString(),
        expectedDuration: formData.expectedDuration,
        vehiclePlate: formData.vehiclePlate,
        accompaniedCount: formData.accompaniedCount,
        specialRequirements: formData.specialRequirements,
        photo: capturedPhoto || undefined,
      };

      // 提交到后端
      const result = await hardware.submitVisitorRegistration(registration);

      if (result?.success) {
        // 添加到本地状态
        const visitor = {
          id: result.visitor_id,
          ...registration,
          status: 'pending' as const,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          isVip: false,
          tags: [],
        };

        addVisitor(visitor);

        message.success('访客登记提交成功');
        navigate('/visitor/waiting', {
          state: {
            visitorId: result.visitor_id,
            estimatedTime: result.estimated_approval_time,
          },
        });
      } else {
        message.error('提交失败，请重试');
      }
    } catch (error) {
      message.error('提交时发生错误');
    } finally {
      setLoading(false);
    }
  };

  // 渲染基本信息表单
  const renderBasicInfoForm = () => (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <Form.Item name="name" label="姓名" rules={[{ required: true, message: '请输入姓名' }]}>
          <Input prefix={<UserOutlined />} placeholder="请输入真实姓名" size="large" />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="phone"
          label="手机号"
          rules={[
            { required: true, message: '请输入手机号' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
          ]}
        >
          <Input prefix={<PhoneOutlined />} placeholder="请输入手机号" size="large" />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="idCard"
          label="身份证号"
          rules={[
            { required: true, message: '请输入身份证号' },
            {
              pattern:
                /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
              message: '请输入正确的身份证号',
            },
          ]}
        >
          <Input prefix={<IdcardOutlined />} placeholder="请输入身份证号" size="large" />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="company"
          label="公司名称"
          rules={[{ required: true, message: '请输入公司名称' }]}
        >
          <Input prefix={<BankOutlined />} placeholder="请输入公司名称" size="large" />
        </Form.Item>
      </Col>
    </Row>
  );

  // 渲染访问信息表单
  const renderVisitInfoForm = () => (
    <Row gutter={[16, 16]}>
      <Col span={24}>
        <Form.Item
          name="purpose"
          label="访问目的"
          rules={[{ required: true, message: '请选择访问目的' }]}
        >
          <Select placeholder="请选择访问目的" size="large">
            <Option value="business_meeting">商务会议</Option>
            <Option value="interview">面试</Option>
            <Option value="delivery">送货</Option>
            <Option value="maintenance">维修</Option>
            <Option value="visit">拜访</Option>
            <Option value="other">其他</Option>
          </Select>
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="visitee"
          label="被访问人"
          rules={[{ required: true, message: '请输入被访问人姓名' }]}
        >
          <Input prefix={<UserOutlined />} placeholder="请输入被访问人姓名" size="large" />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="visiteePhone"
          label="被访问人电话"
          rules={[
            { required: true, message: '请输入被访问人电话' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' },
          ]}
        >
          <Input prefix={<PhoneOutlined />} placeholder="请输入被访问人电话" size="large" />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="department"
          label="访问部门"
          rules={[{ required: true, message: '请选择访问部门' }]}
        >
          <Select placeholder="请选择访问部门" size="large">
            <Option value="sales">销售部</Option>
            <Option value="marketing">市场部</Option>
            <Option value="hr">人力资源部</Option>
            <Option value="it">技术部</Option>
            <Option value="finance">财务部</Option>
            <Option value="admin">行政部</Option>
            <Option value="other">其他</Option>
          </Select>
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="appointmentTime"
          label="预约时间"
          rules={[{ required: true, message: '请选择预约时间' }]}
        >
          <DatePicker
            showTime
            placeholder="请选择预约时间"
            size="large"
            style={{ width: '100%' }}
            disabledDate={(current) => current && current < dayjs().startOf('day')}
          />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item
          name="expectedDuration"
          label="预计访问时长（分钟）"
          rules={[{ required: true, message: '请输入预计访问时长' }]}
          initialValue={60}
        >
          <InputNumber
            min={15}
            max={480}
            step={15}
            placeholder="请输入预计访问时长"
            size="large"
            style={{ width: '100%' }}
            addonAfter="分钟"
          />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item name="vehiclePlate" label="车牌号（可选）">
          <Input placeholder="如有车辆请输入车牌号" size="large" />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item name="accompaniedCount" label="随行人数" initialValue={0}>
          <InputNumber
            min={0}
            max={5}
            placeholder="随行人数"
            size="large"
            style={{ width: '100%' }}
            addonAfter="人"
          />
        </Form.Item>
      </Col>

      <Col span={24}>
        <Form.Item name="specialRequirements" label="特殊要求（可选）">
          <TextArea rows={3} placeholder="如有特殊要求请说明" maxLength={200} showCount />
        </Form.Item>
      </Col>
    </Row>
  );

  // 渲染拍照界面
  const renderPhotoCapture = () => (
    <div style={{ textAlign: 'center', padding: '40px 20px' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <CameraOutlined style={{ fontSize: 64, color: '#1890ff' }} />

        <div>
          <Title level={4}>拍摄证件照</Title>
          <Text type="secondary">请正面面向摄像头，确保光线充足，面部清晰可见</Text>
        </div>

        {capturedPhoto ? (
          <div>
            <img
              src={capturedPhoto}
              alt="访客照片"
              style={{
                maxWidth: 200,
                maxHeight: 200,
                border: '2px solid #d9d9d9',
                borderRadius: 8,
              }}
            />
            <div style={{ marginTop: 16 }}>
              <Button type="primary" onClick={handleCapturePhoto} loading={loading}>
                重新拍摄
              </Button>
            </div>
          </div>
        ) : (
          <Button
            type="primary"
            size="large"
            icon={<CameraOutlined />}
            onClick={handleCapturePhoto}
            loading={loading}
          >
            开始拍摄
          </Button>
        )}
      </Space>
    </div>
  );

  // 渲染确认信息
  const renderConfirmation = () => {
    const formData = form.getFieldsValue();

    return (
      <div style={{ padding: '20px 0' }}>
        <Title level={4} style={{ marginBottom: 24 }}>
          请确认以下信息
        </Title>

        <Card title="基本信息" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <Text strong>姓名：</Text>
              {formData.name}
            </Col>
            <Col span={12}>
              <Text strong>手机号：</Text>
              {formData.phone}
            </Col>
            <Col span={12}>
              <Text strong>身份证：</Text>
              {formData.idCard}
            </Col>
            <Col span={12}>
              <Text strong>公司：</Text>
              {formData.company}
            </Col>
          </Row>
        </Card>

        <Card title="访问信息" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <Text strong>访问目的：</Text>
              {formData.purpose}
            </Col>
            <Col span={12}>
              <Text strong>被访问人：</Text>
              {formData.visitee}
            </Col>
            <Col span={12}>
              <Text strong>联系电话：</Text>
              {formData.visiteePhone}
            </Col>
            <Col span={12}>
              <Text strong>访问部门：</Text>
              {formData.department}
            </Col>
            <Col span={12}>
              <Text strong>预约时间：</Text>
              {formData.appointmentTime?.format('YYYY-MM-DD HH:mm')}
            </Col>
            <Col span={12}>
              <Text strong>预计时长：</Text>
              {formData.expectedDuration}分钟
            </Col>
            {formData.vehiclePlate && (
              <Col span={12}>
                <Text strong>车牌号：</Text>
                {formData.vehiclePlate}
              </Col>
            )}
            <Col span={12}>
              <Text strong>随行人数：</Text>
              {formData.accompaniedCount}人
            </Col>
          </Row>
        </Card>

        {capturedPhoto && (
          <Card title="证件照">
            <img
              src={capturedPhoto}
              alt="访客照片"
              style={{
                maxWidth: 150,
                maxHeight: 150,
                border: '1px solid #d9d9d9',
                borderRadius: 4,
              }}
            />
          </Card>
        )}
      </div>
    );
  };

  return (
    <div style={{ padding: '40px 60px' }}>
      <div style={{ marginBottom: 40 }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 8 }}>
          访客登记
        </Title>
        <Text type="secondary" style={{ display: 'block', textAlign: 'center' }}>
          请按照步骤完成访客登记流程
        </Text>
      </div>

      <Steps current={currentStep} items={steps} style={{ marginBottom: 40 }} />

      <Card>
        <Form form={form} layout="vertical" size="large">
          {currentStep === 0 && renderBasicInfoForm()}
          {currentStep === 1 && renderVisitInfoForm()}
          {currentStep === 2 && renderPhotoCapture()}
          {currentStep === 3 && renderConfirmation()}
        </Form>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space size="middle">
            {currentStep > 0 && (
              <Button size="large" onClick={handlePrev}>
                上一步
              </Button>
            )}

            {currentStep < steps.length - 1 ? (
              <Button type="primary" size="large" onClick={handleNext}>
                下一步
              </Button>
            ) : (
              <Button type="primary" size="large" onClick={handleSubmit} loading={loading}>
                提交登记
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default RegistrationPage;
