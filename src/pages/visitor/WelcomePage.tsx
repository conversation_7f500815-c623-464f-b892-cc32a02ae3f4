// 访客欢迎页面
import React from 'react';
import { Button, Typography, Space, Card } from 'antd';
import { UserAddOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const WelcomePage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div style={{ padding: '60px 40px', textAlign: 'center' }}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <SafetyOutlined style={{ fontSize: 80, color: '#1890ff' }} />
        
        <div>
          <Title level={1} style={{ margin: 0, color: '#262626' }}>
            欢迎使用访客登记系统
          </Title>
          <Text type="secondary" style={{ fontSize: 16 }}>
            请按照提示完成访客登记流程
          </Text>
        </div>

        <Card 
          style={{ 
            maxWidth: 400, 
            margin: '0 auto',
            textAlign: 'left',
          }}
        >
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <Text strong>登记流程：</Text>
            <div>
              <Text>1. 填写个人信息</Text>
            </div>
            <div>
              <Text>2. 拍摄个人照片</Text>
            </div>
            <div>
              <Text>3. 等待审批通过</Text>
            </div>
            <div>
              <Text>4. 获取访客凭证</Text>
            </div>
          </Space>
        </Card>

        <Button
          type="primary"
          size="large"
          icon={<UserAddOutlined />}
          onClick={() => navigate('/visitor/registration')}
          style={{ 
            height: 50,
            fontSize: 16,
            paddingLeft: 32,
            paddingRight: 32,
          }}
        >
          开始登记
        </Button>

        <Text type="secondary" style={{ fontSize: 12 }}>
          如需帮助，请联系前台工作人员
        </Text>
      </Space>
    </div>
  );
};

export default WelcomePage;
