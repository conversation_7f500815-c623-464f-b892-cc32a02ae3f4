// 登记成功页面
import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Result,
  QRCode,
  Descriptions,
  Divider,
  message,
  Row,
  Col,
} from 'antd';
import {
  CheckCircleOutlined,
  PrinterOutlined,
  HomeOutlined,
  DownloadOutlined,
  QrcodeOutlined,
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { useVisitorHardware } from '../../hooks/useVisitorHardware';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;

interface LocationState {
  visitorId: string;
  passNumber: string;
  qrCode: string;
  validUntil: string;
}

const SuccessPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const hardware = useVisitorHardware();

  const [printing, setPrinting] = useState(false);
  const [visitorInfo, setVisitorInfo] = useState<any>(null);

  const state = location.state as LocationState;

  useEffect(() => {
    if (!state?.visitorId) {
      navigate('/visitor');
      return;
    }

    // 这里可以获取完整的访客信息用于显示
    setVisitorInfo({
      name: '张三', // 这些信息应该从状态或API获取
      company: '测试公司',
      purpose: '商务会议',
      visitee: '李四',
      department: '销售部',
    });
  }, [state, navigate]);

  // 打印通行证
  const handlePrint = async () => {
    if (!visitorInfo || !state) return;

    setPrinting(true);
    try {
      const result = await hardware.printVisitorPass(
        visitorInfo.name,
        visitorInfo.company,
        visitorInfo.purpose,
        dayjs(state.validUntil).format('YYYY-MM-DD HH:mm'),
        state.qrCode
      );

      if (result?.status === 'completed') {
        message.success('通行证打印成功！');
      } else {
        message.error('打印失败，请联系工作人员');
      }
    } catch (error) {
      message.error('打印时发生错误');
    } finally {
      setPrinting(false);
    }
  };

  // 下载二维码
  const handleDownloadQR = () => {
    // 这里可以实现二维码下载功能
    message.info('二维码下载功能开发中');
  };

  if (!state) {
    return (
      <Result
        status="warning"
        title="页面访问异常"
        subTitle="请重新进行访客登记"
        extra={
          <Button type="primary" onClick={() => navigate('/visitor')}>
            返回首页
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: '40px 60px' }}>
      <Result
        status="success"
        title="访客登记成功！"
        subTitle="您的访客申请已通过审批，请保存好您的通行证信息"
        icon={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
      />

      <Row gutter={[24, 24]} style={{ marginTop: 40 }}>
        {/* 通行证信息 */}
        <Col xs={24} lg={12}>
          <Card title="通行证信息" bordered>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="通行证号">
                <Text strong style={{ fontSize: 16, color: '#1890ff' }}>
                  {state.passNumber}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="访客姓名">{visitorInfo?.name}</Descriptions.Item>
              <Descriptions.Item label="公司名称">{visitorInfo?.company}</Descriptions.Item>
              <Descriptions.Item label="访问目的">{visitorInfo?.purpose}</Descriptions.Item>
              <Descriptions.Item label="被访问人">{visitorInfo?.visitee}</Descriptions.Item>
              <Descriptions.Item label="访问部门">{visitorInfo?.department}</Descriptions.Item>
              <Descriptions.Item label="有效期至">
                <Text type="warning">{dayjs(state.validUntil).format('YYYY年MM月DD日 HH:mm')}</Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 二维码 */}
        <Col xs={24} lg={12}>
          <Card title="通行二维码" bordered style={{ textAlign: 'center' }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              <QRCode value={state.qrCode} size={200} icon="/logo.png" errorLevel="H" />
              <div>
                <Paragraph type="secondary" style={{ margin: 0 }}>
                  请使用此二维码进行签到和通行验证
                </Paragraph>
                <Button type="link" icon={<DownloadOutlined />} onClick={handleDownloadQR}>
                  下载二维码
                </Button>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 重要提醒 */}
      <Card
        title="重要提醒"
        style={{ marginTop: 24, backgroundColor: '#f6ffed', borderColor: '#b7eb8f' }}
      >
        <Space direction="vertical" size="small">
          <Text>• 请妥善保管您的通行证号和二维码</Text>
          <Text>• 进入大楼时请出示二维码进行验证</Text>
          <Text>• 访问期间请佩戴访客证件</Text>
          <Text>• 访问结束后请及时办理离场手续</Text>
          <Text>• 如有疑问请联系前台工作人员</Text>
        </Space>
      </Card>

      <Divider />

      {/* 操作按钮 */}
      <div style={{ textAlign: 'center' }}>
        <Space size="large">
          <Button
            type="primary"
            size="large"
            icon={<PrinterOutlined />}
            onClick={handlePrint}
            loading={printing}
          >
            打印通行证
          </Button>

          <Button size="large" icon={<QrcodeOutlined />} onClick={handleDownloadQR}>
            保存二维码
          </Button>

          <Button size="large" icon={<HomeOutlined />} onClick={() => navigate('/visitor')}>
            返回首页
          </Button>
        </Space>
      </div>

      {/* 联系信息 */}
      <Card title="联系我们" style={{ marginTop: 24 }} size="small">
        <Row gutter={[16, 8]}>
          <Col span={12}>
            <Text strong>前台电话：</Text>
            <Text>400-123-4567</Text>
          </Col>
          <Col span={12}>
            <Text strong>安保电话：</Text>
            <Text>400-123-4568</Text>
          </Col>
          <Col span={12}>
            <Text strong>工作时间：</Text>
            <Text>周一至周五 9:00-18:00</Text>
          </Col>
          <Col span={12}>
            <Text strong>地址：</Text>
            <Text>北京市朝阳区某某大厦</Text>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default SuccessPage;
