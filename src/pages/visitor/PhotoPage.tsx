// 访客拍照页面
import React, { useState } from 'react';
import { Card, Typography, Button, Alert, Row, Col, message } from 'antd';
import { CheckOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useVisitorStore } from '../../store/visitorStore';
import CameraCapture from '../../components/common/CameraCapture';

const { Title, Text } = Typography;

const PhotoPage: React.FC = () => {
  const navigate = useNavigate();
  const { currentVisitor, updateVisitorData } = useVisitorStore();

  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);

  // 处理拍照成功
  const handleCaptureSuccess = (imageData: string) => {
    setCapturedPhoto(imageData);
    updateVisitorData({ photo: imageData });
    message.success('照片拍摄成功！');
  };

  // 处理拍照错误
  const handleCaptureError = (error: string) => {
    message.error(error);
  };

  // 确认照片并继续
  const handleConfirm = () => {
    if (!capturedPhoto) {
      message.warning('请先拍摄照片');
      return;
    }

    message.success('照片已确认');
    navigate('/visitor/waiting');
  };

  // 返回上一步
  const handleBack = () => {
    navigate('/visitor/registration');
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack} style={{ marginBottom: 16 }}>
            返回上一步
          </Button>

          <Title level={3} style={{ margin: 0 }}>
            📸 拍摄证件照
          </Title>
          <Text type="secondary">请拍摄一张清晰的证件照，用于访客通行证</Text>
        </div>

        {/* 访客信息显示 */}
        {currentVisitor && (
          <Alert
            message={`访客：${currentVisitor.name} | 公司：${currentVisitor.company || '未填写'}`}
            type="info"
            style={{ marginBottom: 24 }}
          />
        )}

        <Row gutter={[24, 24]}>
          {/* 摄像头拍照区域 */}
          <Col xs={24} lg={16}>
            <Card title="摄像头拍照" size="small">
              <CameraCapture onCapture={handleCaptureSuccess} onError={handleCaptureError} />
            </Card>
          </Col>

          {/* 操作区域 */}
          <Col xs={24} lg={8}>
            <Card title="操作" size="small">
              {capturedPhoto ? (
                <div style={{ textAlign: 'center' }}>
                  <Button
                    type="primary"
                    icon={<CheckOutlined />}
                    onClick={handleConfirm}
                    size="large"
                    style={{ width: '100%' }}
                  >
                    确认照片并继续
                  </Button>
                </div>
              ) : (
                <div style={{ textAlign: 'center', color: '#999' }}>请先拍摄照片</div>
              )}
            </Card>
          </Col>
        </Row>

        {/* 提示信息 */}
        <Alert
          message="拍照提示"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>请确保光线充足，面部清晰可见</li>
              <li>建议正面拍摄，避免侧脸或遮挡</li>
              <li>照片将用于生成访客通行证</li>
              <li>如果拍摄效果不满意，可以重新拍摄</li>
            </ul>
          }
          type="info"
          style={{ marginTop: 24 }}
        />
      </Card>
    </div>
  );
};

export default PhotoPage;
