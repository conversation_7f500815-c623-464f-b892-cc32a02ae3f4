// 等待审批页面
import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Steps,
  Spin,
  Button,
  Space,
  Progress,
  Result,
  Divider,
  Timeline,
} from 'antd';
import {
  ClockCircleOutlined,
  CheckCircleOutlined,
  UserOutlined,
  SafetyOutlined,
  HomeOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { useVisitorHardware } from '../../hooks/useVisitorHardware';

const { Title, Text, Paragraph } = Typography;

interface LocationState {
  visitorId: string;
  estimatedTime?: string;
}

const WaitingPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const hardware = useVisitorHardware();

  const [approvalStatus, setApprovalStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [progress, setProgress] = useState(0);

  const state = location.state as LocationState;
  const visitorId = state?.visitorId;

  // 轮询查询审批状态
  useEffect(() => {
    if (!visitorId) {
      navigate('/visitor');
      return;
    }

    const checkStatus = async () => {
      try {
        const result = await hardware.getVisitorApprovalStatus(visitorId);
        if (result) {
          setApprovalStatus(result);

          // 根据状态更新进度
          switch (result.status) {
            case 'pending':
              setProgress(25);
              break;
            case 'visitee_approved':
              setProgress(50);
              break;
            case 'security_review':
              setProgress(75);
              break;
            case 'approved':
              setProgress(100);
              // 延迟跳转到成功页面
              setTimeout(() => {
                navigate('/visitor/success', {
                  state: {
                    visitorId,
                    passNumber: result.pass_number,
                    qrCode: result.qr_code,
                    validUntil: result.valid_until,
                  },
                });
              }, 2000);
              break;
            case 'rejected':
              setProgress(0);
              break;
          }
        }
      } catch (error) {
        console.error('查询审批状态失败:', error);
      } finally {
        setLoading(false);
      }
    };

    // 立即查询一次
    checkStatus();

    // 每5秒查询一次状态
    const interval = setInterval(checkStatus, 5000);

    return () => clearInterval(interval);
  }, [visitorId, navigate, hardware]);

  const getStatusSteps = () => {
    const steps = [
      {
        title: '提交申请',
        description: '访客信息已提交',
        icon: <UserOutlined />,
        status: 'finish',
      },
      {
        title: '被访问人确认',
        description: '等待被访问人确认',
        icon: <UserOutlined />,
        status:
          approvalStatus?.status === 'pending'
            ? 'process'
            : approvalStatus?.status === 'visitee_approved' ||
              approvalStatus?.status === 'security_review' ||
              approvalStatus?.status === 'approved'
            ? 'finish'
            : 'wait',
      },
      {
        title: '安保审批',
        description: '安保部门审核中',
        icon: <SafetyOutlined />,
        status:
          approvalStatus?.status === 'security_review'
            ? 'process'
            : approvalStatus?.status === 'approved'
            ? 'finish'
            : 'wait',
      },
      {
        title: '审批完成',
        description: '可以开始访问',
        icon: <CheckCircleOutlined />,
        status: approvalStatus?.status === 'approved' ? 'finish' : 'wait',
      },
    ];

    return steps;
  };

  const getTimelineItems = () => {
    const items = [
      {
        color: 'green',
        dot: <CheckCircleOutlined />,
        children: (
          <div>
            <Text strong>访客登记提交</Text>
            <br />
            <Text type="secondary">您的访客申请已成功提交</Text>
            <br />
            <Text type="secondary">{new Date().toLocaleString()}</Text>
          </div>
        ),
      },
    ];

    if (
      approvalStatus?.status === 'visitee_approved' ||
      approvalStatus?.status === 'security_review' ||
      approvalStatus?.status === 'approved'
    ) {
      items.push({
        color: 'green',
        dot: <CheckCircleOutlined />,
        children: (
          <div>
            <Text strong>被访问人已确认</Text>
            <br />
            <Text type="secondary">被访问人已同意您的访问申请</Text>
            <br />
            <Text type="secondary">{approvalStatus.visitee_approval_time}</Text>
          </div>
        ),
      });
    }

    if (approvalStatus?.status === 'approved') {
      items.push({
        color: 'green',
        dot: <CheckCircleOutlined />,
        children: (
          <div>
            <Text strong>安保审批通过</Text>
            <br />
            <Text type="secondary">安保部门已批准您的访问申请</Text>
            <br />
            <Text type="secondary">{approvalStatus.approval_time}</Text>
          </div>
        ),
      });
    }

    if (approvalStatus?.status === 'rejected') {
      items.push({
        color: 'red',
        children: (
          <div>
            <Text strong>申请被拒绝</Text>
            <br />
            <Text type="secondary">
              拒绝原因: {approvalStatus.rejection_reason || '未提供原因'}
            </Text>
            <br />
            <Text type="secondary">{approvalStatus.rejection_time}</Text>
          </div>
        ),
      });
    }

    return items;
  };

  if (loading && !approvalStatus) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 400,
        }}
      >
        <Spin size="large" indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
      </div>
    );
  }

  if (approvalStatus?.status === 'rejected') {
    return (
      <div style={{ padding: '40px 60px' }}>
        <Result
          status="error"
          title="申请被拒绝"
          subTitle={`拒绝原因: ${approvalStatus.rejection_reason || '未提供具体原因'}`}
          extra={[
            <Button type="primary" key="retry" onClick={() => navigate('/visitor/registration')}>
              重新申请
            </Button>,
            <Button key="home" onClick={() => navigate('/visitor')}>
              返回首页
            </Button>,
          ]}
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '40px 60px' }}>
      <div style={{ marginBottom: 40, textAlign: 'center' }}>
        <Title level={2}>
          <ClockCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          等待审批
        </Title>
        <Paragraph type="secondary">
          您的访客申请正在审批中，请耐心等待
          {state?.estimatedTime && `，预计审批时间：${state.estimatedTime}`}
        </Paragraph>
      </div>

      <Card style={{ marginBottom: 24 }}>
        <div style={{ marginBottom: 24 }}>
          <Text strong>审批进度</Text>
          <Progress
            percent={progress}
            status={approvalStatus?.status === 'approved' ? 'success' : 'active'}
            style={{ marginTop: 8 }}
          />
        </div>

        <Steps
          current={getStatusSteps().findIndex((step) => step.status === 'process')}
          items={getStatusSteps()}
          direction="vertical"
          size="small"
        />
      </Card>

      <Card title="审批记录">
        <Timeline items={getTimelineItems()} />
      </Card>

      <Divider />

      <div style={{ textAlign: 'center' }}>
        <Space size="middle">
          <Button icon={<HomeOutlined />} onClick={() => navigate('/visitor')}>
            返回首页
          </Button>

          <Button
            type="primary"
            loading={loading}
            onClick={async () => {
              setLoading(true);
              try {
                const result = await hardware.getVisitorApprovalStatus(visitorId);
                if (result) {
                  setApprovalStatus(result);
                }
              } catch (error) {
                console.error('刷新状态失败:', error);
              } finally {
                setLoading(false);
              }
            }}
          >
            刷新状态
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default WaitingPage;
