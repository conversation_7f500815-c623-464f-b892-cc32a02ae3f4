// 硬件测试页面
import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Space,
  Row,
  Col,
  message,
  Descriptions,
  Tag,
  Divider,
  Image,
} from 'antd';
import {
  CameraOutlined,
  PrinterOutlined,
  ScanOutlined,
  MonitorOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { useVisitorHardware } from '../../hooks/useVisitorHardware';
import { useHardware } from '../../hooks/useHardware';

const { Title, Text } = Typography;

const HardwareTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState<string | null>(null);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);

  const visitorHardware = useVisitorHardware();
  const hardware = useHardware();

  // 测试摄像头
  const testCamera = async () => {
    setLoading('camera');
    try {
      const cameras = await visitorHardware.getAvailableCameras();
      if (cameras && cameras.length > 0) {
        const photo = await visitorHardware.captureVisitorPhoto(cameras[0].id, '测试用户');
        if (photo?.success) {
          setCapturedPhoto(photo.image_data || null);
          setTestResults((prev) => ({
            ...prev,
            camera: { success: true, message: '摄像头工作正常' },
          }));
          message.success('摄像头测试成功');
        } else {
          setTestResults((prev) => ({
            ...prev,
            camera: { success: false, message: photo?.error_message || '拍照失败' },
          }));
          message.error('摄像头测试失败');
        }
      } else {
        setTestResults((prev) => ({
          ...prev,
          camera: { success: false, message: '未找到可用摄像头' },
        }));
        message.error('未找到可用摄像头');
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        camera: { success: false, message: '摄像头测试异常' },
      }));
      message.error('摄像头测试异常');
    } finally {
      setLoading(null);
    }
  };

  // 测试打印机
  const testPrinter = async () => {
    setLoading('printer');
    try {
      const printers = await visitorHardware.getPrinterStatus();
      if (printers && printers.length > 0) {
        const printJob = await visitorHardware.printVisitorPass(
          '测试访客',
          '测试公司',
          '硬件测试',
          new Date().toISOString(),
          'TEST_QR_CODE'
        );
        if (printJob?.status === 'completed') {
          setTestResults((prev) => ({
            ...prev,
            printer: { success: true, message: '打印机工作正常' },
          }));
          message.success('打印机测试成功');
        } else {
          setTestResults((prev) => ({
            ...prev,
            printer: { success: false, message: '打印任务失败' },
          }));
          message.error('打印机测试失败');
        }
      } else {
        setTestResults((prev) => ({
          ...prev,
          printer: { success: false, message: '未找到可用打印机' },
        }));
        message.error('未找到可用打印机');
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        printer: { success: false, message: '打印机测试异常' },
      }));
      message.error('打印机测试异常');
    } finally {
      setLoading(null);
    }
  };

  // 测试扫描仪
  const testScanner = async () => {
    setLoading('scanner');
    try {
      const result = await visitorHardware.scanIdCard();
      if (result?.success) {
        setTestResults((prev) => ({
          ...prev,
          scanner: { success: true, message: '扫描仪工作正常', data: result },
        }));
        message.success('扫描仪测试成功');
      } else {
        setTestResults((prev) => ({ ...prev, scanner: { success: false, message: '扫描失败' } }));
        message.error('扫描仪测试失败');
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        scanner: { success: false, message: '扫描仪测试异常' },
      }));
      message.error('扫描仪测试异常');
    } finally {
      setLoading(null);
    }
  };

  // 测试双屏显示
  const testDualScreen = async () => {
    setLoading('display');
    try {
      const visitorResult = await visitorHardware.setDualScreenContent('visitor', 'welcome', {
        message: '欢迎使用访客系统',
      });

      const securityResult = await visitorHardware.setDualScreenContent('security', 'monitor', {
        message: '安保监控界面',
      });

      if (visitorResult && securityResult) {
        setTestResults((prev) => ({
          ...prev,
          display: { success: true, message: '双屏显示工作正常' },
        }));
        message.success('双屏显示测试成功');
      } else {
        setTestResults((prev) => ({
          ...prev,
          display: { success: false, message: '双屏显示设置失败' },
        }));
        message.error('双屏显示测试失败');
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        display: { success: false, message: '双屏显示测试异常' },
      }));
      message.error('双屏显示测试异常');
    } finally {
      setLoading(null);
    }
  };

  // 综合硬件自检
  const runFullCheck = async () => {
    setLoading('full');
    try {
      const result = await visitorHardware.hardwareSelfCheck();
      if (result?.overall_status === 'healthy') {
        setTestResults((prev) => ({
          ...prev,
          full: { success: true, message: '硬件自检通过', data: result },
        }));
        message.success('硬件自检完成');
      } else {
        setTestResults((prev) => ({
          ...prev,
          full: { success: false, message: '硬件自检发现问题', data: result },
        }));
        message.warning('硬件自检发现问题');
      }
    } catch (error) {
      setTestResults((prev) => ({ ...prev, full: { success: false, message: '硬件自检异常' } }));
      message.error('硬件自检异常');
    } finally {
      setLoading(null);
    }
  };

  // 获取状态图标
  const getStatusIcon = (success?: boolean) => {
    if (success === undefined) return null;
    return success ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
    );
  };

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={3}>硬件设备测试</Title>
        <Text type="secondary">测试访客系统相关硬件设备的工作状态</Text>
      </div>

      {/* 测试按钮 */}
      <Card title="硬件测试" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Button
              type="primary"
              icon={loading === 'camera' ? <LoadingOutlined /> : <CameraOutlined />}
              onClick={testCamera}
              loading={loading === 'camera'}
              block
              size="large"
            >
              测试摄像头
            </Button>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Button
              type="primary"
              icon={loading === 'printer' ? <LoadingOutlined /> : <PrinterOutlined />}
              onClick={testPrinter}
              loading={loading === 'printer'}
              block
              size="large"
            >
              测试打印机
            </Button>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Button
              type="primary"
              icon={loading === 'scanner' ? <LoadingOutlined /> : <ScanOutlined />}
              onClick={testScanner}
              loading={loading === 'scanner'}
              block
              size="large"
            >
              测试扫描仪
            </Button>
          </Col>

          <Col xs={24} sm={12} lg={6}>
            <Button
              type="primary"
              icon={loading === 'display' ? <LoadingOutlined /> : <MonitorOutlined />}
              onClick={testDualScreen}
              loading={loading === 'display'}
              block
              size="large"
            >
              测试双屏
            </Button>
          </Col>
        </Row>

        <Divider />

        <Button
          type="default"
          size="large"
          icon={loading === 'full' ? <LoadingOutlined /> : <CheckCircleOutlined />}
          onClick={runFullCheck}
          loading={loading === 'full'}
          style={{ width: '100%' }}
        >
          运行完整硬件自检
        </Button>
      </Card>

      {/* 测试结果 */}
      <Row gutter={[16, 16]}>
        {/* 摄像头测试结果 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <CameraOutlined />
                摄像头测试
                {getStatusIcon(testResults.camera?.success)}
              </Space>
            }
            size="small"
          >
            {testResults.camera ? (
              <div>
                <Tag color={testResults.camera.success ? 'success' : 'error'}>
                  {testResults.camera.success ? '正常' : '异常'}
                </Tag>
                <Text>{testResults.camera.message}</Text>
                {capturedPhoto && (
                  <div style={{ marginTop: 16, textAlign: 'center' }}>
                    <Image
                      src={capturedPhoto}
                      alt="测试照片"
                      width={200}
                      height={150}
                      style={{ objectFit: 'cover', borderRadius: 8 }}
                    />
                  </div>
                )}
              </div>
            ) : (
              <Text type="secondary">点击上方按钮开始测试</Text>
            )}
          </Card>
        </Col>

        {/* 打印机测试结果 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <PrinterOutlined />
                打印机测试
                {getStatusIcon(testResults.printer?.success)}
              </Space>
            }
            size="small"
          >
            {testResults.printer ? (
              <div>
                <Tag color={testResults.printer.success ? 'success' : 'error'}>
                  {testResults.printer.success ? '正常' : '异常'}
                </Tag>
                <Text>{testResults.printer.message}</Text>
              </div>
            ) : (
              <Text type="secondary">点击上方按钮开始测试</Text>
            )}
          </Card>
        </Col>

        {/* 扫描仪测试结果 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <ScanOutlined />
                扫描仪测试
                {getStatusIcon(testResults.scanner?.success)}
              </Space>
            }
            size="small"
          >
            {testResults.scanner ? (
              <div>
                <Tag color={testResults.scanner.success ? 'success' : 'error'}>
                  {testResults.scanner.success ? '正常' : '异常'}
                </Tag>
                <Text>{testResults.scanner.message}</Text>
                {testResults.scanner.data && (
                  <div style={{ marginTop: 8 }}>
                    <Descriptions size="small" column={1}>
                      <Descriptions.Item label="姓名">
                        {testResults.scanner.data.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="身份证号">
                        {testResults.scanner.data.id_number}
                      </Descriptions.Item>
                    </Descriptions>
                  </div>
                )}
              </div>
            ) : (
              <Text type="secondary">点击上方按钮开始测试</Text>
            )}
          </Card>
        </Col>

        {/* 双屏测试结果 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <MonitorOutlined />
                双屏显示测试
                {getStatusIcon(testResults.display?.success)}
              </Space>
            }
            size="small"
          >
            {testResults.display ? (
              <div>
                <Tag color={testResults.display.success ? 'success' : 'error'}>
                  {testResults.display.success ? '正常' : '异常'}
                </Tag>
                <Text>{testResults.display.message}</Text>
              </div>
            ) : (
              <Text type="secondary">点击上方按钮开始测试</Text>
            )}
          </Card>
        </Col>
      </Row>

      {/* 完整自检结果 */}
      {testResults.full && (
        <Card
          title="完整硬件自检结果"
          style={{ marginTop: 16 }}
          extra={getStatusIcon(testResults.full.success)}
        >
          <Tag color={testResults.full.success ? 'success' : 'error'}>
            {testResults.full.success ? '自检通过' : '发现问题'}
          </Tag>
          <Text>{testResults.full.message}</Text>

          {testResults.full.data && (
            <div style={{ marginTop: 16 }}>
              <Descriptions title="设备详情" size="small" column={2}>
                {Object.entries(testResults.full.data.devices || {}).map(
                  ([device, info]: [string, any]) => (
                    <Descriptions.Item key={device} label={device} span={2}>
                      <Space>
                        <Tag color={info.status === 'healthy' ? 'success' : 'warning'}>
                          {info.status}
                        </Tag>
                        <Text>{info.message}</Text>
                      </Space>
                    </Descriptions.Item>
                  )
                )}
              </Descriptions>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default HardwareTestPage;
