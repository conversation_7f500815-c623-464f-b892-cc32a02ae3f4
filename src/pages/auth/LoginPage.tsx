// 登录页面
import React from 'react';
import { Form, Input, Button, Checkbox, message, Space } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';

interface LoginFormData {
  username: string;
  password: string;
  remember: boolean;
}

const LoginPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { login, isLoading, error } = useAuthStore();
  
  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  const handleSubmit = async (values: LoginFormData) => {
    try {
      await login({
        username: values.username,
        password: values.password,
      });
      
      message.success('登录成功！');
      navigate(from, { replace: true });
    } catch (error) {
      message.error('登录失败，请检查用户名和密码');
    }
  };

  return (
    <Form
      form={form}
      name="login"
      onFinish={handleSubmit}
      autoComplete="off"
      size="large"
    >
      <Form.Item
        name="username"
        rules={[
          { required: true, message: '请输入用户名' },
          { min: 3, message: '用户名至少3个字符' },
        ]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="用户名"
          autoComplete="username"
        />
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码' },
          { min: 6, message: '密码至少6个字符' },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
          autoComplete="current-password"
        />
      </Form.Item>

      <Form.Item>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form.Item name="remember" valuePropName="checked" noStyle>
            <Checkbox>记住我</Checkbox>
          </Form.Item>
          <Button type="link" style={{ padding: 0 }}>
            忘记密码？
          </Button>
        </Space>
      </Form.Item>

      {error && (
        <Form.Item>
          <div style={{ color: '#ff4d4f', textAlign: 'center' }}>
            {error}
          </div>
        </Form.Item>
      )}

      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={isLoading}
          style={{ width: '100%' }}
        >
          登录
        </Button>
      </Form.Item>

      <div style={{ textAlign: 'center', marginTop: 16 }}>
        <Space direction="vertical" size="small">
          <div style={{ color: '#666', fontSize: 12 }}>
            测试账号：
          </div>
          <div style={{ color: '#666', fontSize: 12 }}>
            管理员: admin / 123456
          </div>
          <div style={{ color: '#666', fontSize: 12 }}>
            保安: security / 123456
          </div>
        </Space>
      </div>
    </Form>
  );
};

export default LoginPage;
