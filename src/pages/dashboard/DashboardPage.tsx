// 仪表板页面
import React, { useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Space, Button } from 'antd';
import {
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { useVisitorStore } from '../../store/visitorStore';
import { useHardwareStore } from '../../store/hardwareStore';

const { Title, Text } = Typography;

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, hasRole } = useAuthStore();
  const { visitors } = useVisitorStore();
  const { isHardwareReady, getHardwareStatus } = useHardwareStore();

  // 统计数据
  const todayVisitors = visitors.filter(v => 
    new Date(v.createdAt).toDateString() === new Date().toDateString()
  );
  
  const pendingCount = visitors.filter(v => v.status === 'pending').length;
  const approvedCount = visitors.filter(v => v.status === 'approved').length;
  const checkedInCount = visitors.filter(v => v.status === 'checked_in').length;
  
  const hardwareStatus = getHardwareStatus();

  useEffect(() => {
    // 页面加载时可以刷新数据
  }, []);

  const quickActions = [
    ...(hasRole('security') || hasRole('admin') ? [
      {
        title: '访客审批',
        description: `${pendingCount} 个待审批`,
        path: '/security/approval',
        type: 'primary' as const,
      },
      {
        title: '实时监控',
        description: '查看当前访客状态',
        path: '/security/monitor',
        type: 'default' as const,
      },
    ] : []),
    ...(hasRole('admin') ? [
      {
        title: '系统管理',
        description: '用户和系统设置',
        path: '/admin',
        type: 'default' as const,
      },
    ] : []),
    {
      title: '硬件测试',
      description: '检查设备状态',
      path: '/settings/hardware',
      type: 'default' as const,
    },
  ];

  return (
    <Space direction="vertical" size="large" style={{ width: '100%' }}>
      <div>
        <Title level={2}>
          欢迎回来，{user?.name}！
        </Title>
        <Text type="secondary">
          今天是 {new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
          })}
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日访客"
              value={todayVisitors.length}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix={
                <span style={{ fontSize: 14 }}>
                  <ArrowUpOutlined style={{ color: '#52c41a' }} /> 12%
                </span>
              }
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="待审批"
              value={pendingCount}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已通过"
              value={approvedCount}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="在访中"
              value={checkedInCount}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 硬件状态 */}
      <Card title="硬件设备状态">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Statistic
                title="摄像头"
                value={hardwareStatus.camera ? '正常' : '异常'}
                valueStyle={{ 
                  color: hardwareStatus.camera ? '#52c41a' : '#ff4d4f',
                  fontSize: 16,
                }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Statistic
                title="打印机"
                value={hardwareStatus.printer ? '正常' : '异常'}
                valueStyle={{ 
                  color: hardwareStatus.printer ? '#52c41a' : '#ff4d4f',
                  fontSize: 16,
                }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Statistic
                title="扫描仪"
                value={hardwareStatus.scanner ? '正常' : '异常'}
                valueStyle={{ 
                  color: hardwareStatus.scanner ? '#52c41a' : '#ff4d4f',
                  fontSize: 16,
                }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card size="small">
              <Statistic
                title="网络"
                value={hardwareStatus.network ? '已连接' : '断开'}
                valueStyle={{ 
                  color: hardwareStatus.network ? '#52c41a' : '#ff4d4f',
                  fontSize: 16,
                }}
              />
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 快捷操作 */}
      <Card title="快捷操作">
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card 
                size="small" 
                hoverable
                onClick={() => navigate(action.path)}
                style={{ cursor: 'pointer' }}
              >
                <Space direction="vertical">
                  <Text strong>{action.title}</Text>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    {action.description}
                  </Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </Space>
  );
};

export default DashboardPage;
