// 访客审批页面
import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Avatar,
  Descriptions,
  Row,
  Col,
  Badge,
  Tooltip,
  Image,
} from 'antd';
import {
  CheckOutlined,
  CloseOutlined,
  EyeOutlined,
  UserOutlined,
  PhoneOutlined,
  BankOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useVisitorStore } from '../../store/visitorStore';
import { useAuthStore } from '../../store/authStore';
import { useVisitorHardware } from '../../hooks/useVisitorHardware';
import type { Visitor } from '../../store/visitorStore';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const ApprovalPage: React.FC = () => {
  const [selectedVisitor, setSelectedVisitor] = useState<Visitor | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'reject'>('approve');
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const { visitors, updateVisitor, approveVisitor, rejectVisitor } = useVisitorStore();
  const { user } = useAuthStore();
  const hardware = useVisitorHardware();

  // 获取待审批的访客列表
  const pendingVisitors = visitors.filter((visitor) => visitor.status === 'pending');

  // 处理审批
  const handleApproval = async (visitor: Visitor, action: 'approve' | 'reject') => {
    setSelectedVisitor(visitor);
    setApprovalAction(action);
    setApprovalModalVisible(true);
  };

  // 提交审批
  const handleSubmitApproval = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (!selectedVisitor || !user) return;

      if (approvalAction === 'approve') {
        approveVisitor(selectedVisitor.id, user.name, values.comments);
        message.success('访客申请已批准');
      } else {
        rejectVisitor(selectedVisitor.id, user.name, values.comments || '未提供原因');
        message.success('访客申请已拒绝');
      }

      // 这里可以调用后端API同步状态
      // await hardware.submitApproval({
      //   visitor_id: selectedVisitor.id,
      //   action: approvalAction,
      //   approver_id: user.id,
      //   approver_name: user.name,
      //   comments: values.comments,
      // });

      setApprovalModalVisible(false);
      form.resetFields();
      setSelectedVisitor(null);
    } catch (error) {
      message.error('审批处理失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看详情
  const handleViewDetail = (visitor: Visitor) => {
    setSelectedVisitor(visitor);
    setDetailModalVisible(true);
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'orange', text: '待审批' },
      approved: { color: 'green', text: '已批准' },
      rejected: { color: 'red', text: '已拒绝' },
      checked_in: { color: 'blue', text: '已签到' },
      checked_out: { color: 'default', text: '已签退' },
      expired: { color: 'default', text: '已过期' },
    };

    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns: ColumnsType<Visitor> = [
    {
      title: '访客信息',
      key: 'visitor',
      render: (_, record) => (
        <Space>
          <Avatar src={record.photo} icon={<UserOutlined />} size="large" />
          <div>
            <div>
              <Text strong>{record.name}</Text>
              {record.isVip && (
                <Tag color="gold" style={{ marginLeft: 8 }}>
                  VIP
                </Tag>
              )}
            </div>
            <Text type="secondary">{record.company}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Text>
            <PhoneOutlined /> {record.phone}
          </Text>
          <Text>
            <BankOutlined /> {record.idCard}
          </Text>
        </Space>
      ),
    },
    {
      title: '访问信息',
      key: 'visit',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Text>
            <strong>目的：</strong>
            {record.purpose}
          </Text>
          <Text>
            <strong>被访问人：</strong>
            {record.visitee}
          </Text>
          <Text>
            <strong>部门：</strong>
            {record.department}
          </Text>
        </Space>
      ),
    },
    {
      title: '预约时间',
      dataIndex: 'appointmentTime',
      key: 'appointmentTime',
      render: (time) => (
        <Space direction="vertical" size="small">
          <Text>{dayjs(time).format('YYYY-MM-DD')}</Text>
          <Text>{dayjs(time).format('HH:mm')}</Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '提交时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (time) => (
        <Tooltip title={dayjs(time).format('YYYY-MM-DD HH:mm:ss')}>
          <Text type="secondary">{dayjs(time).fromNow()}</Text>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)}>
            详情
          </Button>
          {record.status === 'pending' && (
            <>
              <Button
                type="primary"
                size="small"
                icon={<CheckOutlined />}
                onClick={() => handleApproval(record, 'approve')}
              >
                批准
              </Button>
              <Button
                danger
                size="small"
                icon={<CloseOutlined />}
                onClick={() => handleApproval(record, 'reject')}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={3}>
          <ExclamationCircleOutlined style={{ marginRight: 8, color: '#faad14' }} />
          访客审批
        </Title>
        <Paragraph type="secondary">审核访客申请，确保访问安全合规</Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Badge count={pendingVisitors.length} overflowCount={99}>
                <ClockCircleOutlined style={{ fontSize: 32, color: '#faad14' }} />
              </Badge>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">待审批</Text>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                  {pendingVisitors.length}
                </div>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CheckOutlined style={{ fontSize: 32, color: '#52c41a' }} />
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">今日已批准</Text>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                  {
                    visitors.filter((v) => v.status === 'approved' && dayjs(v.updatedAt).isToday())
                      .length
                  }
                </div>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <CloseOutlined style={{ fontSize: 32, color: '#ff4d4f' }} />
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">今日已拒绝</Text>
                <div style={{ fontSize: 24, fontWeight: 'bold', color: '#ff4d4f' }}>
                  {
                    visitors.filter((v) => v.status === 'rejected' && dayjs(v.updatedAt).isToday())
                      .length
                  }
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 访客列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={pendingVisitors}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="访客详细信息"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          selectedVisitor?.status === 'pending' && (
            <Space key="actions">
              <Button
                type="primary"
                icon={<CheckOutlined />}
                onClick={() => {
                  setDetailModalVisible(false);
                  handleApproval(selectedVisitor, 'approve');
                }}
              >
                批准
              </Button>
              <Button
                danger
                icon={<CloseOutlined />}
                onClick={() => {
                  setDetailModalVisible(false);
                  handleApproval(selectedVisitor, 'reject');
                }}
              >
                拒绝
              </Button>
            </Space>
          ),
        ]}
        width={800}
      >
        {selectedVisitor && (
          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <div style={{ textAlign: 'center' }}>
                {selectedVisitor.photo ? (
                  <Image
                    src={selectedVisitor.photo}
                    alt="访客照片"
                    width={150}
                    height={200}
                    style={{ objectFit: 'cover', borderRadius: 8 }}
                  />
                ) : (
                  <div
                    style={{
                      width: 150,
                      height: 200,
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 8,
                      margin: '0 auto',
                    }}
                  >
                    <UserOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                  </div>
                )}
                <div style={{ marginTop: 16 }}>
                  <Title level={4}>{selectedVisitor.name}</Title>
                  <Text type="secondary">{selectedVisitor.company}</Text>
                </div>
              </div>
            </Col>

            <Col xs={24} md={16}>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="手机号码">{selectedVisitor.phone}</Descriptions.Item>
                <Descriptions.Item label="身份证号">{selectedVisitor.idCard}</Descriptions.Item>
                <Descriptions.Item label="访问目的">{selectedVisitor.purpose}</Descriptions.Item>
                <Descriptions.Item label="被访问人">{selectedVisitor.visitee}</Descriptions.Item>
                <Descriptions.Item label="被访问人电话">
                  {selectedVisitor.visiteePhone}
                </Descriptions.Item>
                <Descriptions.Item label="访问部门">{selectedVisitor.department}</Descriptions.Item>
                <Descriptions.Item label="预约时间">
                  {dayjs(selectedVisitor.appointmentTime).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                <Descriptions.Item label="预计时长">
                  {selectedVisitor.expectedDuration} 分钟
                </Descriptions.Item>
                {selectedVisitor.vehiclePlate && (
                  <Descriptions.Item label="车牌号">
                    {selectedVisitor.vehiclePlate}
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="随行人数">
                  {selectedVisitor.accompaniedCount} 人
                </Descriptions.Item>
                <Descriptions.Item label="申请时间">
                  {dayjs(selectedVisitor.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="当前状态">
                  {getStatusTag(selectedVisitor.status)}
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        )}
      </Modal>

      {/* 审批模态框 */}
      <Modal
        title={approvalAction === 'approve' ? '批准访客申请' : '拒绝访客申请'}
        open={approvalModalVisible}
        onOk={handleSubmitApproval}
        onCancel={() => {
          setApprovalModalVisible(false);
          form.resetFields();
        }}
        confirmLoading={loading}
        okText={approvalAction === 'approve' ? '批准' : '拒绝'}
        okButtonProps={{
          danger: approvalAction === 'reject',
          type: approvalAction === 'approve' ? 'primary' : 'default',
        }}
      >
        {selectedVisitor && (
          <div>
            <div
              style={{ marginBottom: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 8 }}
            >
              <Space>
                <Avatar src={selectedVisitor.photo} icon={<UserOutlined />} />
                <div>
                  <Text strong>{selectedVisitor.name}</Text>
                  <br />
                  <Text type="secondary">{selectedVisitor.company}</Text>
                </div>
              </Space>
            </div>

            <Form form={form} layout="vertical">
              <Form.Item
                name="comments"
                label={approvalAction === 'approve' ? '审批备注（可选）' : '拒绝原因'}
                rules={
                  approvalAction === 'reject' ? [{ required: true, message: '请填写拒绝原因' }] : []
                }
              >
                <TextArea
                  rows={4}
                  placeholder={
                    approvalAction === 'approve' ? '请输入审批备注...' : '请详细说明拒绝原因...'
                  }
                  maxLength={200}
                  showCount
                />
              </Form.Item>
            </Form>

            <div
              style={{
                padding: 12,
                backgroundColor: approvalAction === 'approve' ? '#f6ffed' : '#fff2f0',
                borderRadius: 6,
                border: `1px solid ${approvalAction === 'approve' ? '#b7eb8f' : '#ffccc7'}`,
              }}
            >
              <Text type={approvalAction === 'approve' ? 'success' : 'danger'}>
                {approvalAction === 'approve'
                  ? '✓ 批准后，访客将收到通行证，可以正常访问'
                  : '✗ 拒绝后，访客将收到拒绝通知，无法进入大楼'}
              </Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ApprovalPage;
