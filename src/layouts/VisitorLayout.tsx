// 访客端布局组件
import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout, Typography, Space, Button } from 'antd';
import { HomeOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { useAppStore } from '../store/appStore';

const { Header, Content, Footer } = Layout;
const { Title, Text } = Typography;

const VisitorLayout: React.FC = () => {
  const { deviceInfo } = useAppStore();

  return (
    <Layout style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
    }}>
      <Header style={{
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px',
      }}>
        <Space align="center">
          <HomeOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          <Title level={3} style={{ margin: 0, color: '#262626' }}>
            访客自助登记系统
          </Title>
        </Space>
        
        <Space>
          <Button 
            type="text" 
            icon={<QuestionCircleOutlined />}
            size="large"
          >
            帮助
          </Button>
        </Space>
      </Header>
      
      <Content style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        padding: '24px',
      }}>
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <div style={{
            width: '100%',
            maxWidth: 800,
            background: '#fff',
            borderRadius: 16,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden',
          }}>
            <Outlet />
          </div>
        </div>
      </Content>
      
      <Footer style={{
        textAlign: 'center',
        background: 'transparent',
        color: '#666',
      }}>
        <Space direction="vertical" size="small">
          <Text type="secondary">
            访客管理系统 v1.0.0
          </Text>
          {deviceInfo && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              设备: {deviceInfo.deviceName} | 电量: {deviceInfo.batteryLevel}%
            </Text>
          )}
        </Space>
      </Footer>
    </Layout>
  );
};

export default VisitorLayout;
