// 认证布局组件
import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout, Card, Typography, Space } from 'antd';
import { SafetyOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;

const AuthLayout: React.FC = () => {
  return (
    <Layout style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    }}>
      <Content style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '50px',
      }}>
        <Card
          style={{
            width: '100%',
            maxWidth: 400,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            borderRadius: 16,
          }}
          bodyStyle={{
            padding: '40px 32px',
          }}
        >
          <Space 
            direction="vertical" 
            size="large" 
            style={{ 
              width: '100%',
              textAlign: 'center',
              marginBottom: 32,
            }}
          >
            <SafetyOutlined 
              style={{ 
                fontSize: 48, 
                color: '#1890ff',
              }} 
            />
            <div>
              <Title level={2} style={{ margin: 0, color: '#262626' }}>
                访客管理系统
              </Title>
              <Text type="secondary">
                Visitor Management System
              </Text>
            </div>
          </Space>
          
          <Outlet />
        </Card>
      </Content>
    </Layout>
  );
};

export default AuthLayout;
