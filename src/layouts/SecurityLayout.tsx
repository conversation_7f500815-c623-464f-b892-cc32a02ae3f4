// 保安端布局组件
import React, { useState } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Button,
  Avatar,
  Dropdown,
  Badge,
  Space,
  Typography,
  Alert,
  Statistic,
  Row,
  Col,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  MonitorOutlined,
  TeamOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../store/authStore';
import { useVisitorStore } from '../store/visitorStore';
import { useHardwareStore } from '../store/hardwareStore';

const { Header, Sider, Content } = Layout;
const { Text, Title } = Typography;

const SecurityLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  const { user, logout } = useAuthStore();
  const { visitors } = useVisitorStore();
  const { isHardwareReady } = useHardwareStore();

  // 统计数据
  const pendingCount = visitors.filter(v => v.status === 'pending').length;
  const todayCount = visitors.filter(v => 
    new Date(v.createdAt).toDateString() === new Date().toDateString()
  ).length;

  // 菜单项配置
  const menuItems = [
    {
      key: '/security',
      icon: <DashboardOutlined />,
      label: '安保概览',
    },
    {
      key: '/security/approval',
      icon: <CheckCircleOutlined />,
      label: '访客审批',
    },
    {
      key: '/security/monitor',
      icon: <MonitorOutlined />,
      label: '实时监控',
    },
    {
      key: '/security/visitors',
      icon: <TeamOutlined />,
      label: '访客列表',
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        // 打开个人资料页面
        break;
      case 'logout':
        logout();
        navigate('/auth/login');
        break;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        theme="dark"
        width={250}
      >
        <div style={{ 
          height: 64, 
          margin: 16, 
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#fff',
          fontWeight: 'bold',
          fontSize: collapsed ? 14 : 16,
        }}>
          {collapsed ? '安保' : '安保管理系统'}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
        
        {!collapsed && (
          <div style={{ 
            padding: 16, 
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            marginTop: 'auto',
          }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text style={{ color: 'rgba(255, 255, 255, 0.65)', fontSize: 12 }}>
                今日统计
              </Text>
              <Row gutter={8}>
                <Col span={12}>
                  <Statistic
                    title="待审批"
                    value={pendingCount}
                    valueStyle={{ color: '#faad14', fontSize: 16 }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="今日访客"
                    value={todayCount}
                    valueStyle={{ color: '#52c41a', fontSize: 16 }}
                  />
                </Col>
              </Row>
            </Space>
          </div>
        )}
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: 0, 
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingRight: 24,
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size="middle">
            {!isHardwareReady() && (
              <Badge status="error" text="硬件异常" />
            )}
            
            <Badge count={pendingCount} overflowCount={99}>
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                size="large"
              />
            </Badge>
            
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar 
                  src={user?.avatar} 
                  icon={<UserOutlined />}
                  size="small"
                />
                <Text>{user?.name || '保安'}</Text>
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        {!isHardwareReady() && (
          <Alert
            message="硬件设备异常"
            description="部分硬件设备未正常工作，请检查摄像头、打印机等设备连接状态。"
            type="warning"
            icon={<WarningOutlined />}
            showIcon
            closable
            style={{ margin: '16px 16px 0 16px' }}
          />
        )}
        
        <Content
          style={{
            margin: '24px 16px',
            padding: 24,
            minHeight: 280,
            background: '#fff',
            borderRadius: 8,
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default SecurityLayout;
