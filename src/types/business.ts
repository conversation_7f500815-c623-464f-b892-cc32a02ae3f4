// 业务相关类型定义

// 访客系统核心业务类型
export interface VisitorRegistration {
  // 基本信息
  name: string;
  phone: string;
  idCard: string;
  company: string;
  
  // 访问信息
  purpose: string; // 访问目的
  visitee: string; // 被访问人
  visiteePhone: string;
  visiteeEmail?: string;
  department: string;
  
  // 时间信息
  appointmentTime: string; // 预约时间
  expectedDuration: number; // 预计访问时长（分钟）
  
  // 附加信息
  vehiclePlate?: string; // 车牌号
  accompaniedCount: number; // 随行人数
  accompaniedPersons?: string[]; // 随行人员姓名
  specialRequirements?: string; // 特殊要求
  
  // 证件和照片
  photo?: string; // 访客照片 base64
  idCardFront?: string; // 身份证正面
  idCardBack?: string; // 身份证背面
}

// 访客审批流程
export interface ApprovalWorkflow {
  id: string;
  visitorId: string;
  
  // 审批步骤
  steps: ApprovalStep[];
  currentStep: number;
  
  // 审批状态
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  
  // 时间信息
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface ApprovalStep {
  id: string;
  stepName: string;
  approverRole: 'visitee' | 'department_head' | 'security' | 'admin';
  approver?: string; // 审批人ID
  approverName?: string;
  
  status: 'pending' | 'approved' | 'rejected' | 'skipped';
  approvalTime?: string;
  comments?: string;
  
  // 审批权限
  canApprove: boolean;
  canReject: boolean;
  canDelegate: boolean;
}

// 访客通行凭证
export interface VisitorPass {
  id: string;
  visitorId: string;
  passNumber: string; // 通行证编号
  
  // 通行权限
  accessAreas: string[]; // 可访问区域
  accessLevel: 'visitor' | 'escort_required' | 'restricted';
  
  // 有效期
  validFrom: string;
  validUntil: string;
  
  // 状态
  status: 'active' | 'expired' | 'revoked' | 'suspended';
  
  // 使用记录
  checkInTime?: string;
  checkOutTime?: string;
  actualDuration?: number; // 实际访问时长（分钟）
  
  // 二维码/条形码
  qrCode?: string;
  barcode?: string;
}

// 访客行为记录
export interface VisitorActivity {
  id: string;
  visitorId: string;
  passId: string;
  
  // 活动类型
  activityType: 'check_in' | 'check_out' | 'area_access' | 'security_alert' | 'escort_request';
  
  // 活动详情
  description: string;
  location?: string;
  area?: string;
  
  // 时间和人员
  timestamp: string;
  recordedBy?: string; // 记录人员
  
  // 附加数据
  metadata?: Record<string, any>;
}

// 安全规则配置
export interface SecurityRule {
  id: string;
  name: string;
  description: string;
  
  // 规则类型
  ruleType: 'time_restriction' | 'area_restriction' | 'visitor_limit' | 'blacklist' | 'custom';
  
  // 规则条件
  conditions: SecurityCondition[];
  
  // 规则动作
  actions: SecurityAction[];
  
  // 状态
  isActive: boolean;
  priority: number; // 优先级，数字越大优先级越高
  
  // 时间信息
  createdAt: string;
  updatedAt: string;
}

export interface SecurityCondition {
  field: string; // 字段名，如 'visitor.company', 'time.hour', 'area.name'
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
}

export interface SecurityAction {
  actionType: 'block' | 'require_approval' | 'require_escort' | 'send_alert' | 'log_event';
  parameters?: Record<string, any>;
}

// 访客统计数据
export interface VisitorStatistics {
  // 时间范围
  dateRange: {
    start: string;
    end: string;
  };
  
  // 基础统计
  totalVisitors: number;
  newVisitors: number;
  returningVisitors: number;
  
  // 状态统计
  statusBreakdown: {
    pending: number;
    approved: number;
    rejected: number;
    checked_in: number;
    checked_out: number;
    expired: number;
  };
  
  // 时间分布
  hourlyDistribution: Array<{
    hour: number;
    count: number;
  }>;
  
  // 部门分布
  departmentDistribution: Array<{
    department: string;
    count: number;
  }>;
  
  // 访问目的分布
  purposeDistribution: Array<{
    purpose: string;
    count: number;
  }>;
  
  // 平均访问时长
  averageDuration: number; // 分钟
  
  // 安全事件
  securityEvents: number;
}

// 系统配置
export interface SystemConfig {
  // 访客登记配置
  registration: {
    requirePhoto: boolean;
    requireIdCard: boolean;
    requireVehicleInfo: boolean;
    maxAccompaniedPersons: number;
    allowSelfRegistration: boolean;
  };
  
  // 审批配置
  approval: {
    requireVisiteeApproval: boolean;
    requireSecurityApproval: boolean;
    requireDepartmentHeadApproval: boolean;
    autoApprovalRules: SecurityRule[];
    approvalTimeout: number; // 小时
  };
  
  // 通行配置
  access: {
    defaultAccessLevel: 'visitor' | 'escort_required' | 'restricted';
    maxVisitDuration: number; // 小时
    allowExtension: boolean;
    requireCheckOut: boolean;
  };
  
  // 安全配置
  security: {
    enableBlacklist: boolean;
    enableSecurityAlerts: boolean;
    maxConcurrentVisitors: number;
    restrictedAreas: string[];
    securityRules: SecurityRule[];
  };
  
  // 硬件配置
  hardware: {
    enableFaceRecognition: boolean;
    enablePrinting: boolean;
    enableDualScreen: boolean;
    cameraSettings: {
      resolution: string;
      quality: number;
    };
    printerSettings: {
      paperSize: string;
      printQuality: string;
    };
  };
}

// 通知配置
export interface NotificationConfig {
  id: string;
  name: string;
  eventType: 'visitor_registered' | 'approval_required' | 'visitor_arrived' | 'visitor_departed' | 'security_alert';
  
  // 通知方式
  channels: Array<{
    type: 'email' | 'sms' | 'push' | 'system';
    enabled: boolean;
    template?: string;
  }>;
  
  // 接收人
  recipients: Array<{
    type: 'role' | 'user' | 'department';
    value: string;
  }>;
  
  // 条件
  conditions?: SecurityCondition[];
  
  isActive: boolean;
}
