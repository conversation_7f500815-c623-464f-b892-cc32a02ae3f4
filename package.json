{"name": "android-visitor-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "tauri": "tauri", "android:dev": "tauri android dev", "android:build": "tauri android build"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@hookform/resolvers": "^5.1.1", "@tauri-apps/api": "^2.5.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.26.1", "axios": "^1.10.0", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tauri-apps/cli": "^2.5.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}