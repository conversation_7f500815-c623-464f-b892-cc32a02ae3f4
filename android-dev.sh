#!/bin/bash

# Android 开发环境设置脚本
echo "🔧 设置 Android 开发环境..."

export ANDROID_HOME=~/Library/Android/sdk
export NDK_HOME=$ANDROID_HOME/ndk/26.3.11579264
export JAVA_HOME=/opt/homebrew/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools:$ANDROID_HOME/emulator:$PATH

echo "✅ 环境变量已设置:"
echo "   ANDROID_HOME: $ANDROID_HOME"
echo "   NDK_HOME: $NDK_HOME"
echo "   JAVA_HOME: $JAVA_HOME"

echo "🚀 启动 Android 开发模式..."
pnpm run android:dev
