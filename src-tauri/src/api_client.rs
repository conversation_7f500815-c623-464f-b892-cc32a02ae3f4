// 后端API客户端模块
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct ApiClient {
    base_url: String,
    client: reqwest::Client,
    auth_token: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: String,
    pub error_code: Option<String>,
    pub timestamp: String,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct VisitorData {
    pub id: Option<String>,
    pub name: String,
    pub phone: String,
    pub id_card: String,
    pub company: String,
    pub purpose: String,
    pub visitee: String,
    pub visitee_phone: String,
    pub department: String,
    pub appointment_time: String,
    pub expected_duration: u32,
    pub vehicle_plate: Option<String>,
    pub accompanied_count: u8,
    pub photo: Option<String>,
    pub id_card_photo: Option<String>,
    pub status: String,
    pub created_at: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ApprovalRequest {
    pub visitor_id: String,
    pub action: String, // "approve" or "reject"
    pub approver_id: String,
    pub approver_name: String,
    pub comments: Option<String>,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct VisitorPass {
    pub id: String,
    pub visitor_id: String,
    pub pass_number: String,
    pub qr_code: String,
    pub valid_from: String,
    pub valid_until: String,
    pub access_areas: Vec<String>,
    pub status: String,
}

impl ApiClient {
    pub fn new(base_url: String) -> Self {
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            base_url,
            client,
            auth_token: None,
        }
    }

    pub fn set_auth_token(&mut self, token: String) {
        self.auth_token = Some(token);
    }

    fn get_headers(&self) -> reqwest::header::HeaderMap {
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert(
            reqwest::header::CONTENT_TYPE,
            reqwest::header::HeaderValue::from_static("application/json"),
        );

        if let Some(token) = &self.auth_token {
            headers.insert(
                reqwest::header::AUTHORIZATION,
                reqwest::header::HeaderValue::from_str(&format!("Bearer {}", token))
                    .expect("Invalid auth token"),
            );
        }

        headers
    }

    // 创建访客登记
    pub async fn create_visitor(&self, visitor: &VisitorData) -> Result<ApiResponse<VisitorData>, String> {
        let url = format!("{}/api/visitors", self.base_url);
        
        let response = self
            .client
            .post(&url)
            .headers(self.get_headers())
            .json(visitor)
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<VisitorData>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 获取访客信息
    pub async fn get_visitor(&self, visitor_id: &str) -> Result<ApiResponse<VisitorData>, String> {
        let url = format!("{}/api/visitors/{}", self.base_url, visitor_id);
        
        let response = self
            .client
            .get(&url)
            .headers(self.get_headers())
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<VisitorData>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 更新访客信息
    pub async fn update_visitor(&self, visitor_id: &str, visitor: &VisitorData) -> Result<ApiResponse<VisitorData>, String> {
        let url = format!("{}/api/visitors/{}", self.base_url, visitor_id);
        
        let response = self
            .client
            .put(&url)
            .headers(self.get_headers())
            .json(visitor)
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<VisitorData>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 删除访客
    pub async fn delete_visitor(&self, visitor_id: &str) -> Result<ApiResponse<()>, String> {
        let url = format!("{}/api/visitors/{}", self.base_url, visitor_id);
        
        let response = self
            .client
            .delete(&url)
            .headers(self.get_headers())
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<()>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 获取访客列表
    pub async fn get_visitors(&self, params: Option<HashMap<String, String>>) -> Result<ApiResponse<Vec<VisitorData>>, String> {
        let mut url = format!("{}/api/visitors", self.base_url);
        
        if let Some(params) = params {
            let query_string: Vec<String> = params
                .iter()
                .map(|(k, v)| format!("{}={}", k, v))
                .collect();
            if !query_string.is_empty() {
                url.push_str(&format!("?{}", query_string.join("&")));
            }
        }
        
        let response = self
            .client
            .get(&url)
            .headers(self.get_headers())
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<Vec<VisitorData>>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 提交审批请求
    pub async fn submit_approval(&self, approval: &ApprovalRequest) -> Result<ApiResponse<()>, String> {
        let url = format!("{}/api/approvals", self.base_url);
        
        let response = self
            .client
            .post(&url)
            .headers(self.get_headers())
            .json(approval)
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<()>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 获取访客通行证
    pub async fn get_visitor_pass(&self, visitor_id: &str) -> Result<ApiResponse<VisitorPass>, String> {
        let url = format!("{}/api/visitor-passes/visitor/{}", self.base_url, visitor_id);
        
        let response = self
            .client
            .get(&url)
            .headers(self.get_headers())
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<VisitorPass>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 上传文件
    pub async fn upload_file(&self, file_data: &[u8], file_name: &str, file_type: &str) -> Result<ApiResponse<String>, String> {
        let url = format!("{}/api/upload", self.base_url);
        
        let part = reqwest::multipart::Part::bytes(file_data.to_vec())
            .file_name(file_name.to_string())
            .mime_str(file_type)
            .map_err(|e| format!("创建文件部分失败: {}", e))?;

        let form = reqwest::multipart::Form::new()
            .part("file", part);

        let response = self
            .client
            .post(&url)
            .headers(self.get_headers())
            .multipart(form)
            .send()
            .await
            .map_err(|e| format!("请求失败: {}", e))?;

        if response.status().is_success() {
            response
                .json::<ApiResponse<String>>()
                .await
                .map_err(|e| format!("解析响应失败: {}", e))
        } else {
            Err(format!("API请求失败: {}", response.status()))
        }
    }

    // 健康检查
    pub async fn health_check(&self) -> Result<bool, String> {
        let url = format!("{}/api/health", self.base_url);
        
        let response = self
            .client
            .get(&url)
            .timeout(std::time::Duration::from_secs(5))
            .send()
            .await
            .map_err(|e| format!("健康检查请求失败: {}", e))?;

        Ok(response.status().is_success())
    }
}
