use serde::{Deserialize, Serialize};
use tauri::command;

// 模块声明
mod api_client;
use api_client::{ApiClient, VisitorData, ApprovalRequest};

// 设备信息结构体
#[derive(Serialize, Deserialize)]
pub struct DeviceInfo {
    pub device_name: String,
    pub os_version: String,
    pub app_version: String,
    pub device_id: String,
    pub battery_level: u8,
    pub is_charging: bool,
}

// GPS 位置信息结构体
#[derive(Serialize, Deserialize)]
pub struct LocationInfo {
    pub latitude: f64,
    pub longitude: f64,
    pub accuracy: f32,
    pub timestamp: u64,
}

// 系统信息结构体
#[derive(Serialize, Deserialize)]
pub struct SystemInfo {
    pub total_memory: u64,
    pub available_memory: u64,
    pub cpu_usage: f32,
    pub storage_total: u64,
    pub storage_available: u64,
}

// 网络信息结构体
#[derive(Serialize, Deserialize)]
pub struct NetworkInfo {
    pub connection_type: String,
    pub is_connected: bool,
    pub signal_strength: i32,
    pub ip_address: String,
}

// 访客系统相关结构体
#[derive(Serialize, Deserialize)]
pub struct CameraInfo {
    pub id: String,
    pub name: String,
    pub resolution: String,
    pub status: String,
    pub supports_face_detection: bool,
}

#[derive(Serialize, Deserialize)]
pub struct PhotoResult {
    pub success: bool,
    pub image_data: Option<String>,
    pub file_path: Option<String>,
    pub timestamp: u64,
    pub camera_id: String,
    pub error_message: Option<String>,
}

#[derive(Serialize, Deserialize)]
pub struct PrinterInfo {
    pub id: String,
    pub name: String,
    pub status: String, // "ready", "busy", "error", "offline"
    pub paper_level: u8, // 0-100
    pub supports_color: bool,
}

#[derive(Serialize, Deserialize)]
pub struct PrintJob {
    pub id: String,
    pub document_type: String, // "visitor_pass", "id_card", "receipt"
    pub content: String,
    pub copies: u8,
    pub status: String,
}

// Tauri 命令：获取设备信息
#[command]
async fn get_device_info() -> Result<DeviceInfo, String> {
    // 模拟设备信息
    let device_info = DeviceInfo {
        device_name: "Android Simulator".to_string(),
        os_version: "Android 14 (API 34)".to_string(),
        app_version: "1.0.0".to_string(),
        device_id: "emulator-5554".to_string(),
        battery_level: 85,
        is_charging: false,
    };

    Ok(device_info)
}

// Tauri 命令：获取 GPS 位置
#[command]
async fn get_location() -> Result<LocationInfo, String> {
    // 模拟 GPS 位置（北京天安门）
    let location = LocationInfo {
        latitude: 39.9042,
        longitude: 116.4074,
        accuracy: 10.0,
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
    };

    Ok(location)
}

// Tauri 命令：获取系统信息
#[command]
async fn get_system_info() -> Result<SystemInfo, String> {
    // 模拟系统信息
    let system_info = SystemInfo {
        total_memory: 8 * 1024 * 1024 * 1024, // 8GB
        available_memory: 4 * 1024 * 1024 * 1024, // 4GB
        cpu_usage: 25.5,
        storage_total: 64 * 1024 * 1024 * 1024, // 64GB
        storage_available: 32 * 1024 * 1024 * 1024, // 32GB
    };

    Ok(system_info)
}

// Tauri 命令：获取网络信息
#[command]
async fn get_network_info() -> Result<NetworkInfo, String> {
    // 模拟网络信息
    let network_info = NetworkInfo {
        connection_type: "WiFi".to_string(),
        is_connected: true,
        signal_strength: -45, // dBm
        ip_address: "*************".to_string(),
    };

    Ok(network_info)
}

// Tauri 命令：模拟震动
#[command]
async fn vibrate(duration: u64) -> Result<String, String> {
    // 模拟震动功能
    println!("📳 模拟震动 {} 毫秒", duration);
    Ok(format!("震动 {} 毫秒完成", duration))
}

// Tauri 命令：显示系统通知
#[command]
async fn show_notification(title: String, message: String) -> Result<String, String> {
    // 模拟系统通知
    println!("🔔 通知: {} - {}", title, message);
    Ok("通知已发送".to_string())
}

// Tauri 命令：读取文件
#[command]
async fn read_file(file_path: String) -> Result<String, String> {
    // 模拟文件读取
    match std::fs::read_to_string(&file_path) {
        Ok(content) => Ok(content),
        Err(e) => Err(format!("读取文件失败: {}", e)),
    }
}

// Tauri 命令：写入文件
#[command]
async fn write_file(file_path: String, content: String) -> Result<String, String> {
    // 模拟文件写入
    match std::fs::write(&file_path, content) {
        Ok(_) => Ok("文件写入成功".to_string()),
        Err(e) => Err(format!("写入文件失败: {}", e)),
    }
}

// ========== 访客系统硬件接口 ==========

// 获取可用摄像头列表
#[command]
async fn get_available_cameras() -> Result<Vec<CameraInfo>, String> {
    println!("📸 获取可用摄像头列表");

    let cameras = vec![
        CameraInfo {
            id: "camera_0".to_string(),
            name: "前置摄像头".to_string(),
            resolution: "1920x1080".to_string(),
            status: "available".to_string(),
            supports_face_detection: true,
        },
        CameraInfo {
            id: "camera_1".to_string(),
            name: "后置摄像头".to_string(),
            resolution: "4032x3024".to_string(),
            status: "available".to_string(),
            supports_face_detection: false,
        },
    ];

    Ok(cameras)
}

// 拍摄访客照片
#[command]
async fn capture_visitor_photo(camera_id: String, visitor_name: String) -> Result<PhotoResult, String> {
    println!("📸 为访客 {} 使用摄像头 {} 拍摄照片", visitor_name, camera_id);

    // 模拟拍照过程
    tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

    let result = PhotoResult {
        success: true,
        image_data: Some("data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...".to_string()),
        file_path: Some(format!("/tmp/visitor_photo_{}_{}.jpg", visitor_name, chrono::Utc::now().timestamp())),
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs(),
        camera_id,
        error_message: None,
    };

    println!("✅ 访客照片拍摄完成: {:?}", result.file_path);
    Ok(result)
}

// 获取打印机状态
#[command]
async fn get_printer_status() -> Result<Vec<PrinterInfo>, String> {
    println!("🖨️ 获取打印机状态");

    let printers = vec![
        PrinterInfo {
            id: "printer_main".to_string(),
            name: "访客证件打印机".to_string(),
            status: "ready".to_string(),
            paper_level: 85,
            supports_color: false,
        },
        PrinterInfo {
            id: "printer_backup".to_string(),
            name: "备用打印机".to_string(),
            status: "offline".to_string(),
            paper_level: 0,
            supports_color: true,
        },
    ];

    Ok(printers)
}

// 打印访客通行证
#[command]
async fn print_visitor_pass(
    visitor_name: String,
    company: String,
    visit_purpose: String,
    valid_until: String,
    qr_code: String,
) -> Result<PrintJob, String> {
    println!("🖨️ 打印访客通行证: {} - {}", visitor_name, company);

    // 模拟打印过程
    tokio::time::sleep(tokio::time::Duration::from_millis(3000)).await;

    let print_job = PrintJob {
        id: format!("job_{}", chrono::Utc::now().timestamp()),
        document_type: "visitor_pass".to_string(),
        content: format!("访客: {}\n公司: {}\n目的: {}\n有效期: {}\n二维码: {}",
                        visitor_name, company, visit_purpose, valid_until, qr_code),
        copies: 1,
        status: "completed".to_string(),
    };

    println!("✅ 访客通行证打印完成: {}", print_job.id);
    Ok(print_job)
}

// 扫描身份证
#[command]
async fn scan_id_card() -> Result<serde_json::Value, String> {
    println!("🆔 扫描身份证");

    // 模拟身份证扫描过程
    tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

    let id_card_info = serde_json::json!({
        "success": true,
        "name": "张三",
        "id_number": "110101199001011234",
        "gender": "男",
        "birth_date": "1990-01-01",
        "address": "北京市东城区某某街道123号",
        "issuing_authority": "北京市公安局东城分局",
        "valid_from": "2020-01-01",
        "valid_until": "2030-01-01",
        "photo": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
        "timestamp": chrono::Utc::now().timestamp()
    });

    println!("✅ 身份证扫描完成");
    Ok(id_card_info)
}

// 双屏显示控制
#[command]
async fn set_dual_screen_content(
    screen: String, // "visitor" or "security"
    content_type: String, // "welcome", "form", "waiting", "success", "error"
    data: serde_json::Value,
) -> Result<String, String> {
    println!("🖥️ 设置{}屏幕显示内容: {} - {:?}", screen, content_type, data);

    // 在实际实现中，这里会控制双屏显示不同内容
    // 访客屏：显示欢迎界面、登记表单、等待审批、成功页面等
    // 保安屏：显示访客信息、审批界面、监控画面等

    match screen.as_str() {
        "visitor" => {
            match content_type.as_str() {
                "welcome" => println!("访客屏显示欢迎界面"),
                "form" => println!("访客屏显示登记表单"),
                "waiting" => println!("访客屏显示等待审批"),
                "success" => println!("访客屏显示登记成功"),
                "error" => println!("访客屏显示错误信息"),
                _ => return Err("不支持的内容类型".to_string()),
            }
        },
        "security" => {
            match content_type.as_str() {
                "visitor_info" => println!("保安屏显示访客信息"),
                "approval" => println!("保安屏显示审批界面"),
                "monitor" => println!("保安屏显示监控画面"),
                _ => return Err("不支持的内容类型".to_string()),
            }
        },
        _ => return Err("不支持的屏幕类型".to_string()),
    }

    Ok(format!("{}屏幕内容已更新", screen))
}

// 硬件自检
#[command]
async fn hardware_self_check() -> Result<serde_json::Value, String> {
    println!("🔧 执行硬件自检");

    // 模拟硬件自检过程
    tokio::time::sleep(tokio::time::Duration::from_millis(3000)).await;

    let check_result = serde_json::json!({
        "overall_status": "healthy",
        "timestamp": chrono::Utc::now().timestamp(),
        "devices": {
            "camera": {
                "status": "healthy",
                "message": "摄像头工作正常",
                "details": {
                    "resolution": "1920x1080",
                    "fps": 30,
                    "focus": "auto"
                }
            },
            "printer": {
                "status": "warning",
                "message": "纸张余量不足",
                "details": {
                    "paper_level": 15,
                    "ink_level": 80,
                    "last_maintenance": "2024-01-15"
                }
            },
            "scanner": {
                "status": "healthy",
                "message": "扫描仪工作正常",
                "details": {
                    "resolution": "600dpi",
                    "speed": "normal"
                }
            },
            "display": {
                "status": "healthy",
                "message": "双屏显示正常",
                "details": {
                    "visitor_screen": "1920x1080",
                    "security_screen": "1920x1080",
                    "brightness": 80
                }
            },
            "network": {
                "status": "healthy",
                "message": "网络连接正常",
                "details": {
                    "connection_type": "WiFi",
                    "signal_strength": -45,
                    "speed": "100Mbps"
                }
            }
        }
    });

    println!("✅ 硬件自检完成");
    Ok(check_result)
}

// ========== 后端API调用接口 ==========

// 提交访客登记到后端
#[command]
async fn submit_visitor_registration(visitor_data: serde_json::Value) -> Result<serde_json::Value, String> {
    println!("📤 提交访客登记数据到后端: {:?}", visitor_data);

    // 模拟API调用
    tokio::time::sleep(tokio::time::Duration::from_millis(1500)).await;

    // 在实际实现中，这里会调用后端API
    // let response = reqwest::post("http://api.example.com/visitors")
    //     .json(&visitor_data)
    //     .send()
    //     .await?;

    let response = serde_json::json!({
        "success": true,
        "visitor_id": format!("visitor_{}", chrono::Utc::now().timestamp()),
        "status": "pending_approval",
        "message": "访客登记已提交，等待审批",
        "approval_workflow_id": format!("workflow_{}", chrono::Utc::now().timestamp()),
        "estimated_approval_time": "10-15分钟"
    });

    println!("✅ 访客登记提交成功");
    Ok(response)
}

// 获取访客审批状态
#[command]
async fn get_visitor_approval_status(visitor_id: String) -> Result<serde_json::Value, String> {
    println!("📋 查询访客 {} 的审批状态", visitor_id);

    // 模拟API调用
    tokio::time::sleep(tokio::time::Duration::from_millis(800)).await;

    let response = serde_json::json!({
        "visitor_id": visitor_id,
        "status": "approved",
        "current_step": "security_approval",
        "approved_by": "张保安",
        "approval_time": chrono::Utc::now().to_rfc3339(),
        "pass_number": format!("VP{}", chrono::Utc::now().timestamp()),
        "valid_until": chrono::Utc::now().checked_add_signed(chrono::Duration::hours(8)).unwrap().to_rfc3339(),
        "access_areas": ["lobby", "meeting_room_a"],
        "qr_code": format!("QR_{}", visitor_id)
    });

    println!("✅ 审批状态查询完成");
    Ok(response)
}

// 同步访客数据到后端
#[command]
async fn sync_visitor_data() -> Result<serde_json::Value, String> {
    println!("🔄 同步访客数据到后端");

    // 模拟数据同步过程
    tokio::time::sleep(tokio::time::Duration::from_millis(2000)).await;

    let response = serde_json::json!({
        "success": true,
        "synced_count": 15,
        "failed_count": 0,
        "last_sync_time": chrono::Utc::now().to_rfc3339(),
        "next_sync_time": chrono::Utc::now().checked_add_signed(chrono::Duration::minutes(5)).unwrap().to_rfc3339()
    });

    println!("✅ 数据同步完成");
    Ok(response)
}

// 获取系统配置
#[command]
async fn get_system_config() -> Result<serde_json::Value, String> {
    println!("⚙️ 获取系统配置");

    let config = serde_json::json!({
        "registration": {
            "require_photo": true,
            "require_id_card": true,
            "require_vehicle_info": false,
            "max_accompanied_persons": 3,
            "allow_self_registration": true
        },
        "approval": {
            "require_visitee_approval": true,
            "require_security_approval": true,
            "require_department_head_approval": false,
            "approval_timeout_hours": 24
        },
        "hardware": {
            "enable_face_recognition": true,
            "enable_printing": true,
            "enable_dual_screen": true,
            "camera_resolution": "1920x1080",
            "print_quality": "high"
        },
        "security": {
            "enable_blacklist": true,
            "enable_security_alerts": true,
            "max_concurrent_visitors": 50,
            "restricted_areas": ["server_room", "executive_floor"]
        }
    });

    Ok(config)
}

// ========== 真实后端API调用接口 ==========

// 创建API客户端实例
fn create_api_client() -> ApiClient {
    // 在实际应用中，这个URL应该从配置文件或环境变量读取
    let base_url = std::env::var("API_BASE_URL")
        .unwrap_or_else(|_| "http://localhost:3000".to_string());

    let mut client = ApiClient::new(base_url);

    // 如果有认证token，可以在这里设置
    if let Ok(token) = std::env::var("API_TOKEN") {
        client.set_auth_token(token);
    }

    client
}

// 创建访客记录
#[command]
async fn api_create_visitor(visitor_data: serde_json::Value) -> Result<serde_json::Value, String> {
    println!("📤 调用后端API创建访客记录");

    let client = create_api_client();

    // 将JSON转换为VisitorData结构体
    let visitor: VisitorData = serde_json::from_value(visitor_data)
        .map_err(|e| format!("数据格式错误: {}", e))?;

    match client.create_visitor(&visitor).await {
        Ok(response) => {
            println!("✅ 访客创建成功: {:?}", response);
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 访客创建失败: {}", e);
            Err(e)
        }
    }
}

// 获取访客信息
#[command]
async fn api_get_visitor(visitor_id: String) -> Result<serde_json::Value, String> {
    println!("📋 调用后端API获取访客信息: {}", visitor_id);

    let client = create_api_client();

    match client.get_visitor(&visitor_id).await {
        Ok(response) => {
            println!("✅ 访客信息获取成功");
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 访客信息获取失败: {}", e);
            Err(e)
        }
    }
}

// 更新访客信息
#[command]
async fn api_update_visitor(visitor_id: String, visitor_data: serde_json::Value) -> Result<serde_json::Value, String> {
    println!("📝 调用后端API更新访客信息: {}", visitor_id);

    let client = create_api_client();

    let visitor: VisitorData = serde_json::from_value(visitor_data)
        .map_err(|e| format!("数据格式错误: {}", e))?;

    match client.update_visitor(&visitor_id, &visitor).await {
        Ok(response) => {
            println!("✅ 访客信息更新成功");
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 访客信息更新失败: {}", e);
            Err(e)
        }
    }
}

// 删除访客记录
#[command]
async fn api_delete_visitor(visitor_id: String) -> Result<serde_json::Value, String> {
    println!("🗑️ 调用后端API删除访客记录: {}", visitor_id);

    let client = create_api_client();

    match client.delete_visitor(&visitor_id).await {
        Ok(response) => {
            println!("✅ 访客记录删除成功");
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 访客记录删除失败: {}", e);
            Err(e)
        }
    }
}

// 获取访客列表
#[command]
async fn api_get_visitors(params: Option<serde_json::Value>) -> Result<serde_json::Value, String> {
    println!("📋 调用后端API获取访客列表");

    let client = create_api_client();

    // 转换参数
    let query_params = if let Some(params) = params {
        let map: std::collections::HashMap<String, serde_json::Value> = serde_json::from_value(params)
            .map_err(|e| format!("参数格式错误: {}", e))?;

        Some(map.into_iter()
            .map(|(k, v)| (k, v.to_string()))
            .collect::<std::collections::HashMap<String, String>>())
    } else {
        None
    };

    match client.get_visitors(query_params).await {
        Ok(response) => {
            println!("✅ 访客列表获取成功");
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 访客列表获取失败: {}", e);
            Err(e)
        }
    }
}

// 提交审批请求
#[command]
async fn api_submit_approval(approval_data: serde_json::Value) -> Result<serde_json::Value, String> {
    println!("✅ 调用后端API提交审批请求");

    let client = create_api_client();

    let approval: ApprovalRequest = serde_json::from_value(approval_data)
        .map_err(|e| format!("数据格式错误: {}", e))?;

    match client.submit_approval(&approval).await {
        Ok(response) => {
            println!("✅ 审批请求提交成功");
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 审批请求提交失败: {}", e);
            Err(e)
        }
    }
}

// 获取访客通行证
#[command]
async fn api_get_visitor_pass(visitor_id: String) -> Result<serde_json::Value, String> {
    println!("🎫 调用后端API获取访客通行证: {}", visitor_id);

    let client = create_api_client();

    match client.get_visitor_pass(&visitor_id).await {
        Ok(response) => {
            println!("✅ 访客通行证获取成功");
            serde_json::to_value(response)
                .map_err(|e| format!("响应序列化失败: {}", e))
        },
        Err(e) => {
            println!("❌ 访客通行证获取失败: {}", e);
            Err(e)
        }
    }
}

// 后端健康检查
#[command]
async fn api_health_check() -> Result<serde_json::Value, String> {
    println!("🏥 执行后端API健康检查");

    let client = create_api_client();

    match client.health_check().await {
        Ok(is_healthy) => {
            let response = serde_json::json!({
                "healthy": is_healthy,
                "timestamp": chrono::Utc::now().to_rfc3339(),
                "message": if is_healthy { "后端服务正常" } else { "后端服务异常" }
            });

            println!("✅ 健康检查完成: {}", is_healthy);
            Ok(response)
        },
        Err(e) => {
            println!("❌ 健康检查失败: {}", e);
            Err(e)
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .invoke_handler(tauri::generate_handler![
        get_device_info,
        get_location,
        get_system_info,
        get_network_info,
        vibrate,
        show_notification,
        read_file,
        write_file,
        // 访客系统硬件接口
        get_available_cameras,
        capture_visitor_photo,
        get_printer_status,
        print_visitor_pass,
        scan_id_card,
        set_dual_screen_content,
        hardware_self_check,
        // 后端API接口
        submit_visitor_registration,
        get_visitor_approval_status,
        sync_visitor_data,
        get_system_config,
        // 真实后端API调用
        api_create_visitor,
        api_get_visitor,
        api_update_visitor,
        api_delete_visitor,
        api_get_visitors,
        api_submit_approval,
        api_get_visitor_pass,
        api_health_check
    ])
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }
      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
