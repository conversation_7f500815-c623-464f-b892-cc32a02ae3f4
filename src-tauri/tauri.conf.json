{"$schema": "https://schema.tauri.app/config/2.0.0-rc", "productName": "android-visitor-react", "version": "0.1.0", "identifier": "com.example.androidvisitorreact", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "android": {"devUrl": "http://********:5173"}, "app": {"windows": [{"title": "android-visitor-react", "width": 800, "height": 600, "resizable": true, "fullscreen": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}