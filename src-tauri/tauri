#!/usr/bin/env node
// 这是一个包装脚本，用于解决 Android 构建系统找不到 tauri 命令的问题
import { spawn } from 'child_process';
import { dirname } from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取项目根目录（src-tauri 的父目录）
const projectRoot = dirname(__dirname);

// 使用 npx 运行 tauri 命令
const args = process.argv.slice(2);
const child = spawn('npx', ['tauri', ...args], {
  cwd: projectRoot,
  stdio: 'inherit',
  shell: true,
});

child.on('exit', (code) => {
  process.exit(code);
});
