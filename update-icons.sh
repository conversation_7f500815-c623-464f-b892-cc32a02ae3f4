#!/bin/bash

# Android 图标更新脚本
# 使用方法: ./update-icons.sh your-icon.png

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <图标文件路径>"
    echo "例如: $0 my-new-icon.png"
    exit 1
fi

SOURCE_ICON="$1"

if [ ! -f "$SOURCE_ICON" ]; then
    echo "错误: 找不到图标文件 $SOURCE_ICON"
    exit 1
fi

echo "🎨 开始更新 Android 图标..."

# 检查是否安装了 ImageMagick
if ! command -v convert &> /dev/null; then
    echo "❌ 需要安装 ImageMagick"
    echo "安装命令: brew install imagemagick"
    exit 1
fi

# 创建不同尺寸的图标
ICON_SIZES=(
    "48:mdpi"
    "72:hdpi" 
    "96:xhdpi"
    "144:xxhdpi"
    "192:xxxhdpi"
)

# 生成 Android 图标
for size_info in "${ICON_SIZES[@]}"; do
    IFS=':' read -r size density <<< "$size_info"
    
    echo "📱 生成 ${size}x${size} (${density}) 图标..."
    
    # 生成普通图标
    convert "$SOURCE_ICON" -resize ${size}x${size} \
        "src-tauri/gen/android/app/src/main/res/mipmap-${density}/ic_launcher.png"
    
    # 生成圆形图标
    convert "$SOURCE_ICON" -resize ${size}x${size} \
        "src-tauri/gen/android/app/src/main/res/mipmap-${density}/ic_launcher_round.png"
    
    # 生成前景图标
    convert "$SOURCE_ICON" -resize ${size}x${size} \
        "src-tauri/gen/android/app/src/main/res/mipmap-${density}/ic_launcher_foreground.png"
done

# 更新 Tauri 源图标
echo "🔄 更新 Tauri 源图标..."
cp "$SOURCE_ICON" "src-tauri/icons/icon.png"

# 生成其他尺寸的 Tauri 图标
convert "$SOURCE_ICON" -resize 32x32 "src-tauri/icons/32x32.png"
convert "$SOURCE_ICON" -resize 128x128 "src-tauri/icons/128x128.png"
convert "$SOURCE_ICON" -resize 256x256 "src-tauri/icons/<EMAIL>"

echo "✅ 图标更新完成！"
echo "📦 现在可以重新构建应用: npm run android:build"
